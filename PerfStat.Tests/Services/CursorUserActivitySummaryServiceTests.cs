using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using PerfStat.Models;
using PerfStat.Models.DTOs;
using PerfStat.Repositories;
using PerfStat.Services;
using Xunit;

namespace PerfStat.Tests.Services;

public class CursorUserActivitySummaryServiceTests : TestBase
{
    private readonly Mock<ICursorUserActivitySummaryRepository> _mockRepository;
    private readonly Mock<ILogger<CursorUserActivitySummaryService>> _mockLogger;
    private readonly CursorUserActivitySummaryService _service;

    public CursorUserActivitySummaryServiceTests()
    {
        _mockRepository = new Mock<ICursorUserActivitySummaryRepository>();
        _mockLogger = new Mock<ILogger<CursorUserActivitySummaryService>>();
        _service = new CursorUserActivitySummaryService(_mockRepository.Object, Mapper, _mockLogger.Object);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnDto_WhenEntityExists()
    {
        // Arrange
        var entity = TestDataFactory.CreateCursorUserActivitySummary();
        entity.Id = 1;
        _mockRepository.Setup(x => x.GetByIdAsync(1)).ReturnsAsync(entity);

        // Act
        var result = await _service.GetByIdAsync(1);

        // Assert
        result.Should().NotBeNull();
        result!.Id.Should().Be(1);
        result.UserId.Should().Be(entity.UserId);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenEntityDoesNotExist()
    {
        // Arrange
        _mockRepository.Setup(x => x.GetByIdAsync(999)).ReturnsAsync((CursorUserActivitySummary?)null);

        // Act
        var result = await _service.GetByIdAsync(999);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetAllAsync_ShouldReturnAllDtos()
    {
        // Arrange
        var entities = TestDataFactory.CreateMultipleCursorUserActivitySummaries(3);
        _mockRepository.Setup(x => x.GetAllAsync()).ReturnsAsync(entities);

        // Act
        var result = await _service.GetAllAsync();

        // Assert
        result.Should().HaveCount(3);
        result.Should().AllBeOfType<CursorUserActivitySummaryDto>();
    }

    [Fact]
    public async Task GetByUserIdAndDateAsync_ShouldReturnDto_WhenExists()
    {
        // Arrange
        var entity = TestDataFactory.CreateCursorUserActivitySummary();
        var userId = "test_user";
        var reportDate = DateOnly.FromDateTime(DateTime.Today);
        
        _mockRepository.Setup(x => x.GetByUserIdAndDateAsync(userId, reportDate)).ReturnsAsync(entity);

        // Act
        var result = await _service.GetByUserIdAndDateAsync(userId, reportDate);

        // Assert
        result.Should().NotBeNull();
        result!.UserId.Should().Be(entity.UserId);
    }

    [Fact]
    public async Task CreateAsync_ShouldCreateAndReturnDto()
    {
        // Arrange
        var createDto = TestDataFactory.CreateCursorUserActivitySummaryDto();
        var entity = TestDataFactory.CreateCursorUserActivitySummary();
        entity.Id = 1;

        _mockRepository.Setup(x => x.AddAsync(It.IsAny<CursorUserActivitySummary>())).ReturnsAsync(entity);

        // Act
        var result = await _service.CreateAsync(createDto);

        // Assert
        result.Should().NotBeNull();
        result.Id.Should().Be(1);
        result.UserId.Should().Be(createDto.UserId);
        
        _mockRepository.Verify(x => x.AddAsync(It.IsAny<CursorUserActivitySummary>()), Times.Once);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateAndReturnDto_WhenEntityExists()
    {
        // Arrange
        var existingEntity = TestDataFactory.CreateCursorUserActivitySummary();
        existingEntity.Id = 1;
        
        var updateDto = new UpdateCursorUserActivitySummaryDto
        {
            ChatSuggestedLinesAdded = 200,
            IsActive = false
        };

        _mockRepository.Setup(x => x.GetByIdAsync(1)).ReturnsAsync(existingEntity);
        _mockRepository.Setup(x => x.UpdateAsync(It.IsAny<CursorUserActivitySummary>())).ReturnsAsync(existingEntity);

        // Act
        var result = await _service.UpdateAsync(1, updateDto);

        // Assert
        result.Should().NotBeNull();
        result!.Id.Should().Be(1);
        
        _mockRepository.Verify(x => x.GetByIdAsync(1), Times.Once);
        _mockRepository.Verify(x => x.UpdateAsync(It.IsAny<CursorUserActivitySummary>()), Times.Once);
    }

    [Fact]
    public async Task UpdateAsync_ShouldReturnNull_WhenEntityDoesNotExist()
    {
        // Arrange
        var updateDto = new UpdateCursorUserActivitySummaryDto();
        _mockRepository.Setup(x => x.GetByIdAsync(999)).ReturnsAsync((CursorUserActivitySummary?)null);

        // Act
        var result = await _service.UpdateAsync(999, updateDto);

        // Assert
        result.Should().BeNull();
        _mockRepository.Verify(x => x.UpdateAsync(It.IsAny<CursorUserActivitySummary>()), Times.Never);
    }

    [Fact]
    public async Task DeleteAsync_ShouldReturnTrue_WhenEntityExists()
    {
        // Arrange
        _mockRepository.Setup(x => x.ExistsAsync(1)).ReturnsAsync(true);
        _mockRepository.Setup(x => x.DeleteAsync(1)).Returns(Task.CompletedTask);

        // Act
        var result = await _service.DeleteAsync(1);

        // Assert
        result.Should().BeTrue();
        _mockRepository.Verify(x => x.DeleteAsync(1), Times.Once);
    }

    [Fact]
    public async Task DeleteAsync_ShouldReturnFalse_WhenEntityDoesNotExist()
    {
        // Arrange
        _mockRepository.Setup(x => x.ExistsAsync(999)).ReturnsAsync(false);

        // Act
        var result = await _service.DeleteAsync(999);

        // Assert
        result.Should().BeFalse();
        _mockRepository.Verify(x => x.DeleteAsync(It.IsAny<int>()), Times.Never);
    }

    [Fact]
    public async Task GetByUserIdAsync_ShouldReturnUserDtos()
    {
        // Arrange
        var userId = "test_user";
        var entities = new List<CursorUserActivitySummary>
        {
            TestDataFactory.CreateCursorUserActivitySummary(userId),
            TestDataFactory.CreateCursorUserActivitySummary(userId)
        };
        
        _mockRepository.Setup(x => x.GetByUserIdAsync(userId)).ReturnsAsync(entities);

        // Act
        var result = await _service.GetByUserIdAsync(userId);

        // Assert
        result.Should().HaveCount(2);
        result.Should().OnlyContain(x => x.UserId == userId);
    }

    [Fact]
    public async Task GetByDateRangeAsync_ShouldReturnDtosInRange()
    {
        // Arrange
        var startDate = DateOnly.FromDateTime(DateTime.Today.AddDays(-5));
        var endDate = DateOnly.FromDateTime(DateTime.Today);
        var entities = TestDataFactory.CreateMultipleCursorUserActivitySummaries(3);
        
        _mockRepository.Setup(x => x.GetByDateRangeAsync(startDate, endDate)).ReturnsAsync(entities);

        // Act
        var result = await _service.GetByDateRangeAsync(startDate, endDate);

        // Assert
        result.Should().HaveCount(3);
    }

    [Fact]
    public async Task GetActiveUsersAsync_ShouldReturnActiveDtos()
    {
        // Arrange
        var reportDate = DateOnly.FromDateTime(DateTime.Today);
        var entities = new List<CursorUserActivitySummary>
        {
            TestDataFactory.CreateCursorUserActivitySummary(isActive: true),
            TestDataFactory.CreateCursorUserActivitySummary(isActive: true)
        };
        
        _mockRepository.Setup(x => x.GetActiveUsersAsync(reportDate)).ReturnsAsync(entities);

        // Act
        var result = await _service.GetActiveUsersAsync(reportDate);

        // Assert
        result.Should().HaveCount(2);
        result.Should().OnlyContain(x => x.IsActive);
    }

    [Fact]
    public async Task GetByEmailAsync_ShouldReturnDtosWithEmail()
    {
        // Arrange
        var email = "<EMAIL>";
        var entities = new List<CursorUserActivitySummary>
        {
            TestDataFactory.CreateCursorUserActivitySummary(email: email)
        };
        
        _mockRepository.Setup(x => x.GetByEmailAsync(email)).ReturnsAsync(entities);

        // Act
        var result = await _service.GetByEmailAsync(email);

        // Assert
        result.Should().HaveCount(1);
        result.First().Email.Should().Be(email);
    }

    [Fact]
    public async Task ExistsAsync_ShouldReturnRepositoryResult()
    {
        // Arrange
        _mockRepository.Setup(x => x.ExistsAsync(1)).ReturnsAsync(true);

        // Act
        var result = await _service.ExistsAsync(1);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task GetTotalCountAsync_ShouldReturnRepositoryCount()
    {
        // Arrange
        _mockRepository.Setup(x => x.CountAsync()).ReturnsAsync(10);

        // Act
        var result = await _service.GetTotalCountAsync();

        // Assert
        result.Should().Be(10);
    }

    [Fact]
    public async Task GetPagedAsync_ShouldReturnPagedDtos()
    {
        // Arrange
        var entities = TestDataFactory.CreateMultipleCursorUserActivitySummaries(5);
        _mockRepository.Setup(x => x.GetPagedAsync(1, 5)).ReturnsAsync(entities);

        // Act
        var result = await _service.GetPagedAsync(1, 5);

        // Assert
        result.Should().HaveCount(5);
    }
}
