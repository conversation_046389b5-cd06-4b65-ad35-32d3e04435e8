using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using PerfStat.Data;
using PerfStat.Models.DTOs;
using PerfStat.Tests.Integration;
using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;

namespace PerfStat.Tests.Integration;

public class CursorUserActivitySummaryIntegrationTests : IClassFixture<CustomWebApplicationFactory<Program>>
{
    private readonly HttpClient _client;
    private readonly CustomWebApplicationFactory<Program> _factory;

    public CursorUserActivitySummaryIntegrationTests(CustomWebApplicationFactory<Program> factory)
    {
        _factory = factory;
        _client = factory.CreateClient();
    }

    [Fact]
    public async Task GetAll_ShouldReturnEmptyList_WhenNoData()
    {
        // Act
        var response = await _client.GetAsync("/api/CursorUserActivitySummary");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        var content = await response.Content.ReadAsStringAsync();
        var summaries = JsonSerializer.Deserialize<List<CursorUserActivitySummaryDto>>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });
        summaries.Should().BeEmpty();
    }

    [Fact]
    public async Task CreateAndGet_ShouldWorkCorrectly()
    {
        // Arrange
        var createDto = new CreateCursorUserActivitySummaryDto
        {
            UserId = "integration_test_user",
            ReportDate = DateOnly.FromDateTime(DateTime.Today),
            Email = "<EMAIL>",
            IsActive = true,
            ChatSuggestedLinesAdded = 100,
            ChatAcceptedLinesAdded = 80,
            EditRequests = 30
        };

        // Act - Create
        var createResponse = await _client.PostAsJsonAsync("/api/CursorUserActivitySummary", createDto);

        // Assert - Create
        createResponse.StatusCode.Should().Be(HttpStatusCode.Created);
        var createdContent = await createResponse.Content.ReadAsStringAsync();
        var createdSummary = JsonSerializer.Deserialize<CursorUserActivitySummaryDto>(createdContent, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });
        createdSummary.Should().NotBeNull();
        createdSummary!.Id.Should().BeGreaterThan(0);
        createdSummary.UserId.Should().Be(createDto.UserId);

        // Act - Get by ID
        var getResponse = await _client.GetAsync($"/api/CursorUserActivitySummary/{createdSummary.Id}");

        // Assert - Get
        getResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        var getContent = await getResponse.Content.ReadAsStringAsync();
        var retrievedSummary = JsonSerializer.Deserialize<CursorUserActivitySummaryDto>(getContent, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });
        retrievedSummary.Should().NotBeNull();
        retrievedSummary!.UserId.Should().Be(createDto.UserId);
        retrievedSummary.Email.Should().Be(createDto.Email);
    }

    [Fact]
    public async Task Update_ShouldModifyExistingRecord()
    {
        // Arrange - Create a record first
        var createDto = new CreateCursorUserActivitySummaryDto
        {
            UserId = "update_test_user",
            ReportDate = DateOnly.FromDateTime(DateTime.Today),
            ChatSuggestedLinesAdded = 100
        };

        var createResponse = await _client.PostAsJsonAsync("/api/CursorUserActivitySummary", createDto);
        var createdContent = await createResponse.Content.ReadAsStringAsync();
        var createdSummary = JsonSerializer.Deserialize<CursorUserActivitySummaryDto>(createdContent, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        var updateDto = new UpdateCursorUserActivitySummaryDto
        {
            ChatSuggestedLinesAdded = 200,
            IsActive = false
        };

        // Act - Update
        var updateResponse = await _client.PutAsJsonAsync($"/api/CursorUserActivitySummary/{createdSummary!.Id}", updateDto);

        // Assert - Update
        updateResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        var updatedContent = await updateResponse.Content.ReadAsStringAsync();
        var updatedSummary = JsonSerializer.Deserialize<CursorUserActivitySummaryDto>(updatedContent, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });
        updatedSummary.Should().NotBeNull();
        updatedSummary!.ChatSuggestedLinesAdded.Should().Be(200);
        updatedSummary.IsActive.Should().BeFalse();
    }

    [Fact]
    public async Task Delete_ShouldRemoveRecord()
    {
        // Arrange - Create a record first
        var createDto = new CreateCursorUserActivitySummaryDto
        {
            UserId = "delete_test_user",
            ReportDate = DateOnly.FromDateTime(DateTime.Today)
        };

        var createResponse = await _client.PostAsJsonAsync("/api/CursorUserActivitySummary", createDto);
        var createdContent = await createResponse.Content.ReadAsStringAsync();
        var createdSummary = JsonSerializer.Deserialize<CursorUserActivitySummaryDto>(createdContent, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        // Act - Delete
        var deleteResponse = await _client.DeleteAsync($"/api/CursorUserActivitySummary/{createdSummary!.Id}");

        // Assert - Delete
        deleteResponse.StatusCode.Should().Be(HttpStatusCode.NoContent);

        // Verify deletion
        var getResponse = await _client.GetAsync($"/api/CursorUserActivitySummary/{createdSummary.Id}");
        getResponse.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task GetByUserId_ShouldReturnUserRecords()
    {
        // Arrange - Create multiple records for the same user
        var userId = "multi_record_user";
        var createDtos = new[]
        {
            new CreateCursorUserActivitySummaryDto
            {
                UserId = userId,
                ReportDate = DateOnly.FromDateTime(DateTime.Today),
                ChatSuggestedLinesAdded = 100
            },
            new CreateCursorUserActivitySummaryDto
            {
                UserId = userId,
                ReportDate = DateOnly.FromDateTime(DateTime.Today.AddDays(-1)),
                ChatSuggestedLinesAdded = 150
            }
        };

        foreach (var dto in createDtos)
        {
            await _client.PostAsJsonAsync("/api/CursorUserActivitySummary", dto);
        }

        // Act
        var response = await _client.GetAsync($"/api/CursorUserActivitySummary/user/{userId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        var content = await response.Content.ReadAsStringAsync();
        var summaries = JsonSerializer.Deserialize<List<CursorUserActivitySummaryDto>>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });
        summaries.Should().HaveCount(2);
        summaries.Should().OnlyContain(x => x.UserId == userId);
    }

    [Fact]
    public async Task GetPaged_ShouldReturnPagedResults()
    {
        // Arrange - Create multiple records
        for (int i = 0; i < 15; i++)
        {
            var createDto = new CreateCursorUserActivitySummaryDto
            {
                UserId = $"paged_user_{i}",
                ReportDate = DateOnly.FromDateTime(DateTime.Today.AddDays(-i))
            };
            await _client.PostAsJsonAsync("/api/CursorUserActivitySummary", createDto);
        }

        // Act
        var response = await _client.GetAsync("/api/CursorUserActivitySummary/paged?page=1&pageSize=5");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        var content = await response.Content.ReadAsStringAsync();
        var pagedResult = JsonSerializer.Deserialize<JsonElement>(content);
        
        pagedResult.GetProperty("page").GetInt32().Should().Be(1);
        pagedResult.GetProperty("pageSize").GetInt32().Should().Be(5);
        pagedResult.GetProperty("data").GetArrayLength().Should().Be(5);
        pagedResult.GetProperty("totalCount").GetInt32().Should().BeGreaterThanOrEqualTo(15);
    }

    [Fact]
    public async Task GetNotFound_ShouldReturn404()
    {
        // Act
        var response = await _client.GetAsync("/api/CursorUserActivitySummary/99999");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task CreateWithInvalidData_ShouldReturnBadRequest()
    {
        // Arrange - Create DTO with missing required fields
        var invalidDto = new CreateCursorUserActivitySummaryDto
        {
            // Missing UserId and ReportDate
            Email = "<EMAIL>"
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/CursorUserActivitySummary", invalidDto);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    private async Task SeedTestData()
    {
        using var scope = _factory.Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        
        // Add test data if needed
        var testSummary = TestDataFactory.CreateCursorUserActivitySummary();
        context.CursorUserActivitySummaries.Add(testSummary);
        await context.SaveChangesAsync();
    }
}
