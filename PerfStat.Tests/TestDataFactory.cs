using PerfStat.Models;
using PerfStat.Models.DTOs;

namespace PerfStat.Tests;

public static class TestDataFactory
{
    public static CursorUserActivitySummary CreateCursorUserActivitySummary(
        string userId = "test_user_123",
        DateOnly? reportDate = null,
        string? email = "<EMAIL>",
        bool isActive = true)
    {
        return new CursorUserActivitySummary
        {
            UserId = userId,
            ReportDate = reportDate ?? DateOnly.FromDateTime(DateTime.Today),
            Email = email,
            IsActive = isActive,
            ChatSuggestedLinesAdded = 100,
            ChatSuggestedLinesDeleted = 20,
            ChatAcceptedLinesAdded = 80,
            ChatAcceptedLinesDeleted = 15,
            ChatTotalApplies = 50,
            ChatTotalAccepts = 40,
            ChatTotalRejects = 10,
            ChatTabsShown = 25,
            TabsAccepted = 20,
            EditRequests = 30,
            AskRequests = 15,
            AgentRequests = 5,
            CmdKUsages = 10,
            SubscriptionIncludedReqs = 100,
            ApiKeyReqs = 0,
            UsageBasedReqs = 0,
            BugbotUsages = 2,
            MostUsedModel = "gpt-4",
            MostUsedApplyExtension = ".js",
            MostUsedTabExtension = ".ts",
            ClientVersion = "1.0.0",
            RawDate = "2025-06-04",
            UserName = "Test User",
            WorkEmail = "<EMAIL>"
        };
    }

    public static CreateCursorUserActivitySummaryDto CreateCursorUserActivitySummaryDto(
        string userId = "test_user_123",
        DateOnly? reportDate = null,
        string? email = "<EMAIL>",
        bool isActive = true)
    {
        return new CreateCursorUserActivitySummaryDto
        {
            UserId = userId,
            ReportDate = reportDate ?? DateOnly.FromDateTime(DateTime.Today),
            Email = email,
            IsActive = isActive,
            ChatSuggestedLinesAdded = 100,
            ChatAcceptedLinesAdded = 80,
            EditRequests = 30,
            UserName = "Test User"
        };
    }

    public static XmlyBepUserIdentityMapping CreateXmlyBepUserIdentityMapping(
        string? email = "<EMAIL>",
        int? gitUserId = 12345,
        string? userCorpId = "EMP001")
    {
        return new XmlyBepUserIdentityMapping
        {
            L1 = "Engineering",
            L2 = "Backend",
            L3 = "API Team",
            Email = email,
            GitUserId = gitUserId,
            UserCorpId = userCorpId
        };
    }

    public static CreateXmlyBepUserIdentityMappingDto CreateXmlyBepUserIdentityMappingDto(
        string? email = "<EMAIL>",
        int? gitUserId = 12345,
        string? userCorpId = "EMP001")
    {
        return new CreateXmlyBepUserIdentityMappingDto
        {
            L1 = "Engineering",
            L2 = "Backend",
            L3 = "API Team",
            Email = email,
            GitUserId = gitUserId,
            UserCorpId = userCorpId
        };
    }

    public static List<CursorUserActivitySummary> CreateMultipleCursorUserActivitySummaries(int count = 5)
    {
        var summaries = new List<CursorUserActivitySummary>();
        for (int i = 0; i < count; i++)
        {
            summaries.Add(CreateCursorUserActivitySummary(
                userId: $"user_{i}",
                reportDate: DateOnly.FromDateTime(DateTime.Today.AddDays(-i)),
                email: $"user{i}@example.com",
                isActive: i % 2 == 0
            ));
        }
        return summaries;
    }

    public static List<XmlyBepUserIdentityMapping> CreateMultipleXmlyBepUserIdentityMappings(int count = 5)
    {
        var mappings = new List<XmlyBepUserIdentityMapping>();
        for (int i = 0; i < count; i++)
        {
            mappings.Add(CreateXmlyBepUserIdentityMapping(
                email: $"user{i}@ximalaya.com",
                gitUserId: 12345 + i,
                userCorpId: $"EMP{i:D3}"
            ));
        }
        return mappings;
    }
}
