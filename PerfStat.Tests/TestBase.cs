using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using PerfStat.Data;
using AutoMapper;
using PerfStat.Mappings;

namespace PerfStat.Tests;

public abstract class TestBase : IDisposable
{
    protected readonly ApplicationDbContext Context;
    protected readonly IMapper Mapper;
    protected readonly ILogger Logger;
    protected readonly ServiceProvider ServiceProvider;

    protected TestBase()
    {
        var services = new ServiceCollection();
        
        // 配置内存数据库
        services.AddDbContext<ApplicationDbContext>(options =>
            options.UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString()));
        
        // 配置AutoMapper
        services.AddAutoMapper(typeof(AutoMapperProfile));
        
        // 配置日志
        services.AddLogging(builder => builder.AddConsole());
        
        ServiceProvider = services.BuildServiceProvider();
        
        Context = ServiceProvider.GetRequiredService<ApplicationDbContext>();
        Mapper = ServiceProvider.GetRequiredService<IMapper>();
        Logger = ServiceProvider.GetRequiredService<ILogger<TestBase>>();
        
        // 确保数据库已创建
        Context.Database.EnsureCreated();
    }

    public void Dispose()
    {
        Context?.Dispose();
        ServiceProvider?.Dispose();
    }
}
