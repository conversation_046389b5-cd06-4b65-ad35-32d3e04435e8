{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "ConnectionStrings": {"DefaultConnection": "DataSource=:memory:"}, "CronJobs": {"DailyApiCall": {"CronExpression": "0 0 10 * * ?", "TimeZone": "Asia/Shanghai", "ApiUrl": "http://localhost:5141/api/testapi/daily-task", "HttpMethod": "POST", "Headers": {"Content-Type": "application/json", "User-Agent": "PerfStat-CronJob-Test/1.0"}, "RequestBody": "{\"source\":\"test-cron-job\",\"timestamp\":\"{{timestamp}}\"}"}}}