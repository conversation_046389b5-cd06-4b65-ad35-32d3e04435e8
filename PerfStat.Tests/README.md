# PerfStat 测试套件

## 概述

本测试套件为 PerfStat 项目提供了全面的测试覆盖，包括单元测试、集成测试和端到端(E2E)测试。

## 测试架构

```
PerfStat.Tests/
├── Controllers/           # Controller层单元测试
├── Services/             # Service层单元测试
├── Repositories/         # Repository层单元测试
├── Jobs/                 # 定时任务单元测试
├── Integration/          # 集成测试
├── E2E/                  # 端到端测试
├── TestBase.cs           # 测试基类
├── TestDataFactory.cs    # 测试数据工厂
└── run-tests.sh          # 测试运行脚本
```

## 技术栈

- **xUnit** - 测试框架
- **FluentAssertions** - 断言库
- **Moq** - Mock框架
- **Microsoft.AspNetCore.Mvc.Testing** - ASP.NET Core集成测试
- **Microsoft.EntityFrameworkCore.InMemory** - 内存数据库

## 测试类型

### 1. 单元测试 (Unit Tests)

测试单个组件的功能，使用Mock对象隔离依赖。

#### Repository层测试
- `CursorUserActivitySummaryRepositoryTests` - 用户活动汇总Repository测试
- `XmlyBepUserIdentityMappingRepositoryTests` - 用户身份映射Repository测试

**测试覆盖**：
- CRUD操作
- 复杂查询
- 分页功能
- 错误处理

#### Service层测试
- `CursorUserActivitySummaryServiceTests` - 用户活动汇总Service测试

**测试覆盖**：
- 业务逻辑
- DTO映射
- 异常处理
- 依赖调用

#### Controller层测试
- `CursorUserActivitySummaryControllerTests` - API Controller测试

**测试覆盖**：
- HTTP响应状态码
- 请求/响应数据
- 参数验证
- 错误处理

#### 定时任务测试
- `DailyApiCallJobTests` - 定时任务测试

**测试覆盖**：
- 任务执行逻辑
- 配置读取
- 错误处理
- 日志记录

### 2. 集成测试 (Integration Tests)

测试多个组件之间的交互，使用内存数据库。

#### API集成测试
- `CursorUserActivitySummaryIntegrationTests` - API集成测试

**测试覆盖**：
- 完整的HTTP请求/响应流程
- 数据库操作
- 数据持久化
- API端点功能

### 3. 端到端测试 (E2E Tests)

测试完整的用户场景和工作流程。

#### 完整工作流测试
- `FullWorkflowE2ETests` - 端到端工作流测试

**测试覆盖**：
- 完整用户旅程
- 多API协作
- 数据一致性
- 错误场景

## 运行测试

### 使用脚本运行

```bash
# 运行所有测试
./PerfStat.Tests/run-tests.sh

# 运行特定类型的测试
./PerfStat.Tests/run-tests.sh unit        # 单元测试
./PerfStat.Tests/run-tests.sh integration # 集成测试
./PerfStat.Tests/run-tests.sh e2e         # E2E测试
```

### 使用dotnet命令

```bash
# 运行所有测试
dotnet test PerfStat.Tests/

# 运行特定测试类
dotnet test PerfStat.Tests/ --filter "CursorUserActivitySummaryRepositoryTests"

# 运行特定测试方法
dotnet test PerfStat.Tests/ --filter "AddAsync_ShouldAddEntitySuccessfully"

# 生成代码覆盖率报告
dotnet test PerfStat.Tests/ --collect:"XPlat Code Coverage"
```

### 在IDE中运行

- **Visual Studio**: 使用测试资源管理器
- **VS Code**: 使用 .NET Test Explorer 扩展
- **JetBrains Rider**: 使用内置测试运行器

## 测试数据

### TestDataFactory

提供了创建测试数据的工厂方法：

```csharp
// 创建用户活动汇总实体
var summary = TestDataFactory.CreateCursorUserActivitySummary();

// 创建用户身份映射实体
var mapping = TestDataFactory.CreateXmlyBepUserIdentityMapping();

// 创建多个测试实体
var summaries = TestDataFactory.CreateMultipleCursorUserActivitySummaries(5);
```

### 测试数据库

- **单元测试**: 使用Mock对象，不涉及数据库
- **集成测试**: 使用EF Core内存数据库
- **E2E测试**: 使用独立的内存数据库实例

## 测试配置

### appsettings.Testing.json

测试环境专用配置：

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "DataSource=:memory:"
  },
  "CronJobs": {
    "DailyApiCall": {
      "ApiUrl": "http://localhost:5141/api/testapi/daily-task"
    }
  }
}
```

## 最佳实践

### 1. 测试命名

使用 `MethodName_ShouldExpectedBehavior_WhenCondition` 格式：

```csharp
[Fact]
public async Task GetByIdAsync_ShouldReturnEntity_WhenEntityExists()
```

### 2. 测试结构

使用 AAA (Arrange-Act-Assert) 模式：

```csharp
[Fact]
public async Task TestMethod()
{
    // Arrange
    var entity = TestDataFactory.CreateEntity();
    
    // Act
    var result = await service.MethodAsync(entity);
    
    // Assert
    result.Should().NotBeNull();
}
```

### 3. 断言

使用 FluentAssertions 提供更好的可读性：

```csharp
// 好的做法
result.Should().NotBeNull();
result.Should().HaveCount(5);
result.Should().OnlyContain(x => x.IsActive);

// 避免
Assert.NotNull(result);
Assert.Equal(5, result.Count());
```

### 4. Mock使用

只Mock外部依赖，不Mock被测试的类：

```csharp
// 好的做法
var mockRepository = new Mock<IRepository>();
var service = new Service(mockRepository.Object);

// 避免
var mockService = new Mock<IService>();
```

## 持续集成

### GitHub Actions 示例

```yaml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Setup .NET
      uses: actions/setup-dotnet@v1
      with:
        dotnet-version: 9.0.x
    - name: Run tests
      run: ./PerfStat.Tests/run-tests.sh
```

## 测试覆盖率

目标覆盖率：
- **代码覆盖率**: > 80%
- **分支覆盖率**: > 70%
- **方法覆盖率**: > 90%

查看覆盖率报告：
```bash
dotnet test --collect:"XPlat Code Coverage"
# 报告生成在 TestResults/ 目录
```

## 故障排除

### 常见问题

1. **内存数据库问题**
   - 确保每个测试使用独立的数据库实例
   - 检查 `UseInMemoryDatabase` 配置

2. **Mock设置问题**
   - 验证Mock的Setup是否正确
   - 检查方法签名是否匹配

3. **异步测试问题**
   - 确保使用 `async/await`
   - 避免 `.Result` 或 `.Wait()`

4. **测试隔离问题**
   - 确保测试之间没有共享状态
   - 使用 `IDisposable` 清理资源

### 调试测试

```bash
# 详细输出
dotnet test --verbosity detailed

# 调试特定测试
dotnet test --filter "TestMethodName" --verbosity diagnostic
```

## 贡献指南

1. **添加新测试**：
   - 为新功能编写对应的测试
   - 遵循现有的命名和结构约定
   - 确保测试独立且可重复

2. **修改现有测试**：
   - 更新相关的测试用例
   - 保持测试覆盖率
   - 验证所有测试仍然通过

3. **测试审查**：
   - 检查测试逻辑的正确性
   - 验证测试覆盖了边界情况
   - 确保测试性能合理
