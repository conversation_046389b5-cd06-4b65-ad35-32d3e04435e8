using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using PerfStat.Data;
using PerfStat.Models.DTOs;
using PerfStat.Tests.Integration;
using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;

namespace PerfStat.Tests.E2E;

public class FullWorkflowE2ETests : IClassFixture<CustomWebApplicationFactory<Program>>
{
    private readonly HttpClient _client;
    private readonly CustomWebApplicationFactory<Program> _factory;

    public FullWorkflowE2ETests(CustomWebApplicationFactory<Program> factory)
    {
        _factory = factory;
        _client = factory.CreateClient();
    }

    [Fact]
    public async Task CompleteUserJourney_ShouldWorkEndToEnd()
    {
        // This test simulates a complete user journey through the API
        
        // Step 1: Create a user identity mapping
        var identityDto = new CreateXmlyBepUserIdentityMappingDto
        {
            L1 = "Engineering",
            L2 = "Backend",
            L3 = "API Team",
            Email = "<EMAIL>",
            GitUserId = 54321,
            UserCorpId = "E2E001"
        };

        var identityResponse = await _client.PostAsJsonAsync("/api/XmlyBepUserIdentityMapping", identityDto);
        identityResponse.StatusCode.Should().Be(HttpStatusCode.Created);
        
        var identityContent = await identityResponse.Content.ReadAsStringAsync();
        var createdIdentity = JsonSerializer.Deserialize<XmlyBepUserIdentityMappingDto>(identityContent, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        // Step 2: Create multiple activity summaries for the user
        var activityDtos = new[]
        {
            new CreateCursorUserActivitySummaryDto
            {
                UserId = "e2e_user_001",
                ReportDate = DateOnly.FromDateTime(DateTime.Today),
                Email = "<EMAIL>",
                IsActive = true,
                ChatSuggestedLinesAdded = 150,
                ChatAcceptedLinesAdded = 120,
                EditRequests = 50,
                UserName = "E2E Test User"
            },
            new CreateCursorUserActivitySummaryDto
            {
                UserId = "e2e_user_001",
                ReportDate = DateOnly.FromDateTime(DateTime.Today.AddDays(-1)),
                Email = "<EMAIL>",
                IsActive = true,
                ChatSuggestedLinesAdded = 200,
                ChatAcceptedLinesAdded = 180,
                EditRequests = 75
            },
            new CreateCursorUserActivitySummaryDto
            {
                UserId = "e2e_user_001",
                ReportDate = DateOnly.FromDateTime(DateTime.Today.AddDays(-2)),
                Email = "<EMAIL>",
                IsActive = false,
                ChatSuggestedLinesAdded = 50,
                ChatAcceptedLinesAdded = 40,
                EditRequests = 10
            }
        };

        var createdActivities = new List<CursorUserActivitySummaryDto>();
        foreach (var dto in activityDtos)
        {
            var response = await _client.PostAsJsonAsync("/api/CursorUserActivitySummary", dto);
            response.StatusCode.Should().Be(HttpStatusCode.Created);
            
            var content = await response.Content.ReadAsStringAsync();
            var created = JsonSerializer.Deserialize<CursorUserActivitySummaryDto>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });
            createdActivities.Add(created!);
        }

        // Step 3: Query user identity by email
        var identityByEmailResponse = await _client.GetAsync($"/api/XmlyBepUserIdentityMapping/email/{identityDto.Email}");
        identityByEmailResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var identityByEmailContent = await identityByEmailResponse.Content.ReadAsStringAsync();
        var retrievedIdentity = JsonSerializer.Deserialize<XmlyBepUserIdentityMappingDto>(identityByEmailContent, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });
        retrievedIdentity!.Email.Should().Be(identityDto.Email);

        // Step 4: Query activities by user ID
        var activitiesByUserResponse = await _client.GetAsync("/api/CursorUserActivitySummary/user/e2e_user_001");
        activitiesByUserResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var activitiesByUserContent = await activitiesByUserResponse.Content.ReadAsStringAsync();
        var userActivities = JsonSerializer.Deserialize<List<CursorUserActivitySummaryDto>>(activitiesByUserContent, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });
        userActivities.Should().HaveCount(3);

        // Step 5: Query activities by email
        var activitiesByEmailResponse = await _client.GetAsync($"/api/CursorUserActivitySummary/email/{identityDto.Email}");
        activitiesByEmailResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var activitiesByEmailContent = await activitiesByEmailResponse.Content.ReadAsStringAsync();
        var emailActivities = JsonSerializer.Deserialize<List<CursorUserActivitySummaryDto>>(activitiesByEmailContent, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });
        emailActivities.Should().HaveCount(3);

        // Step 6: Query active users for today
        var activeUsersResponse = await _client.GetAsync($"/api/CursorUserActivitySummary/active-users/{DateOnly.FromDateTime(DateTime.Today)}");
        activeUsersResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var activeUsersContent = await activeUsersResponse.Content.ReadAsStringAsync();
        var activeUsers = JsonSerializer.Deserialize<List<CursorUserActivitySummaryDto>>(activeUsersContent, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });
        activeUsers.Should().Contain(x => x.UserId == "e2e_user_001" && x.IsActive);

        // Step 7: Query by date range
        var startDate = DateOnly.FromDateTime(DateTime.Today.AddDays(-3));
        var endDate = DateOnly.FromDateTime(DateTime.Today);
        var dateRangeResponse = await _client.GetAsync($"/api/CursorUserActivitySummary/date-range?startDate={startDate}&endDate={endDate}");
        dateRangeResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var dateRangeContent = await dateRangeResponse.Content.ReadAsStringAsync();
        var dateRangeActivities = JsonSerializer.Deserialize<List<CursorUserActivitySummaryDto>>(dateRangeContent, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });
        dateRangeActivities.Should().HaveCountGreaterThanOrEqualTo(3);

        // Step 8: Update an activity record
        var updateDto = new UpdateCursorUserActivitySummaryDto
        {
            ChatSuggestedLinesAdded = 300,
            ChatAcceptedLinesAdded = 250,
            EditRequests = 100
        };

        var updateResponse = await _client.PutAsJsonAsync($"/api/CursorUserActivitySummary/{createdActivities[0].Id}", updateDto);
        updateResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var updatedContent = await updateResponse.Content.ReadAsStringAsync();
        var updatedActivity = JsonSerializer.Deserialize<CursorUserActivitySummaryDto>(updatedContent, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });
        updatedActivity!.ChatSuggestedLinesAdded.Should().Be(300);

        // Step 9: Test pagination
        var pagedResponse = await _client.GetAsync("/api/CursorUserActivitySummary/paged?page=1&pageSize=2");
        pagedResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var pagedContent = await pagedResponse.Content.ReadAsStringAsync();
        var pagedResult = JsonSerializer.Deserialize<JsonElement>(pagedContent);
        pagedResult.GetProperty("data").GetArrayLength().Should().BeLessThanOrEqualTo(2);

        // Step 10: Update user identity mapping
        var identityUpdateDto = new UpdateXmlyBepUserIdentityMappingDto
        {
            L2 = "Frontend",
            L3 = "UI Team"
        };

        var identityUpdateResponse = await _client.PutAsJsonAsync($"/api/XmlyBepUserIdentityMapping/{createdIdentity!.Id}", identityUpdateDto);
        identityUpdateResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var identityUpdatedContent = await identityUpdateResponse.Content.ReadAsStringAsync();
        var updatedIdentity = JsonSerializer.Deserialize<XmlyBepUserIdentityMappingDto>(identityUpdatedContent, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });
        updatedIdentity!.L2.Should().Be("Frontend");
        updatedIdentity.L3.Should().Be("UI Team");

        // Step 11: Test existence checks
        var existsResponse = await _client.SendAsync(new HttpRequestMessage(HttpMethod.Head, $"/api/CursorUserActivitySummary/{createdActivities[0].Id}"));
        existsResponse.StatusCode.Should().Be(HttpStatusCode.OK);

        var notExistsResponse = await _client.SendAsync(new HttpRequestMessage(HttpMethod.Head, "/api/CursorUserActivitySummary/99999"));
        notExistsResponse.StatusCode.Should().Be(HttpStatusCode.NotFound);

        // Step 12: Clean up - Delete records
        foreach (var activity in createdActivities)
        {
            var deleteResponse = await _client.DeleteAsync($"/api/CursorUserActivitySummary/{activity.Id}");
            deleteResponse.StatusCode.Should().Be(HttpStatusCode.NoContent);
        }

        var identityDeleteResponse = await _client.DeleteAsync($"/api/XmlyBepUserIdentityMapping/{createdIdentity.Id}");
        identityDeleteResponse.StatusCode.Should().Be(HttpStatusCode.NoContent);

        // Step 13: Verify deletion
        var verifyDeleteResponse = await _client.GetAsync($"/api/CursorUserActivitySummary/{createdActivities[0].Id}");
        verifyDeleteResponse.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task CronJobManagement_ShouldWorkEndToEnd()
    {
        // Test the cron job management endpoints
        
        // Step 1: Check job status
        var statusResponse = await _client.GetAsync("/api/cronjob/status");
        statusResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var statusContent = await statusResponse.Content.ReadAsStringAsync();
        var statusResult = JsonSerializer.Deserialize<JsonElement>(statusContent);
        statusResult.GetProperty("jobs").GetArrayLength().Should().BeGreaterThan(0);

        // Step 2: Check job configuration
        var configResponse = await _client.GetAsync("/api/cronjob/config");
        configResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var configContent = await configResponse.Content.ReadAsStringAsync();
        var configResult = JsonSerializer.Deserialize<JsonElement>(configContent);
        configResult.GetProperty("cronJobs").GetProperty("DailyApiCall").ValueKind.Should().Be(JsonValueKind.Object);

        // Step 3: Test manual trigger (this might fail if the target API is not available, but should not return 500)
        var triggerResponse = await _client.PostAsync("/api/cronjob/trigger/DailyApiCall", null);
        triggerResponse.StatusCode.Should().BeOneOf(HttpStatusCode.OK, HttpStatusCode.BadRequest, HttpStatusCode.InternalServerError);
        // Note: We allow InternalServerError here because the target API might not be available in test environment
    }

    [Fact]
    public async Task ErrorHandling_ShouldWorkCorrectly()
    {
        // Test various error scenarios
        
        // Test 404 for non-existent resources
        var notFoundResponse = await _client.GetAsync("/api/CursorUserActivitySummary/99999");
        notFoundResponse.StatusCode.Should().Be(HttpStatusCode.NotFound);

        // Test 400 for invalid data
        var invalidDto = new { invalidField = "invalid" };
        var badRequestResponse = await _client.PostAsJsonAsync("/api/CursorUserActivitySummary", invalidDto);
        badRequestResponse.StatusCode.Should().Be(HttpStatusCode.BadRequest);

        // Test 400 for invalid pagination parameters
        var invalidPaginationResponse = await _client.GetAsync("/api/CursorUserActivitySummary/paged?page=0&pageSize=0");
        invalidPaginationResponse.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }
}
