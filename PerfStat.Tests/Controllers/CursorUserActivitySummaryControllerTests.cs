using FluentAssertions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using PerfStat.Controllers;
using PerfStat.Models.DTOs;
using PerfStat.Services;
using Xunit;

namespace PerfStat.Tests.Controllers;

public class CursorUserActivitySummaryControllerTests
{
    private readonly Mock<ICursorUserActivitySummaryService> _mockService;
    private readonly Mock<ILogger<CursorUserActivitySummaryController>> _mockLogger;
    private readonly CursorUserActivitySummaryController _controller;

    public CursorUserActivitySummaryControllerTests()
    {
        _mockService = new Mock<ICursorUserActivitySummaryService>();
        _mockLogger = new Mock<ILogger<CursorUserActivitySummaryController>>();
        _controller = new CursorUserActivitySummaryController(_mockService.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task GetAll_ShouldReturnOkWithSummaries()
    {
        // Arrange
        var summaries = new List<CursorUserActivitySummaryDto>
        {
            new() { Id = 1, UserId = "user1" },
            new() { Id = 2, UserId = "user2" }
        };
        _mockService.Setup(x => x.GetAllAsync()).ReturnsAsync(summaries);

        // Act
        var result = await _controller.GetAll();

        // Assert
        var okResult = result.Result.Should().BeOfType<OkObjectResult>().Subject;
        var returnedSummaries = okResult.Value.Should().BeAssignableTo<IEnumerable<CursorUserActivitySummaryDto>>().Subject;
        returnedSummaries.Should().HaveCount(2);
    }

    [Fact]
    public async Task GetById_ShouldReturnOk_WhenSummaryExists()
    {
        // Arrange
        var summary = new CursorUserActivitySummaryDto { Id = 1, UserId = "user1" };
        _mockService.Setup(x => x.GetByIdAsync(1)).ReturnsAsync(summary);

        // Act
        var result = await _controller.GetById(1);

        // Assert
        var okResult = result.Result.Should().BeOfType<OkObjectResult>().Subject;
        var returnedSummary = okResult.Value.Should().BeOfType<CursorUserActivitySummaryDto>().Subject;
        returnedSummary.Id.Should().Be(1);
    }

    [Fact]
    public async Task GetById_ShouldReturnNotFound_WhenSummaryDoesNotExist()
    {
        // Arrange
        _mockService.Setup(x => x.GetByIdAsync(999)).ReturnsAsync((CursorUserActivitySummaryDto?)null);

        // Act
        var result = await _controller.GetById(999);

        // Assert
        result.Result.Should().BeOfType<NotFoundObjectResult>();
    }

    [Fact]
    public async Task GetByUserIdAndDate_ShouldReturnOk_WhenSummaryExists()
    {
        // Arrange
        var userId = "test_user";
        var reportDate = DateOnly.FromDateTime(DateTime.Today);
        var summary = new CursorUserActivitySummaryDto { Id = 1, UserId = userId, ReportDate = reportDate };
        
        _mockService.Setup(x => x.GetByUserIdAndDateAsync(userId, reportDate)).ReturnsAsync(summary);

        // Act
        var result = await _controller.GetByUserIdAndDate(userId, reportDate);

        // Assert
        var okResult = result.Result.Should().BeOfType<OkObjectResult>().Subject;
        var returnedSummary = okResult.Value.Should().BeOfType<CursorUserActivitySummaryDto>().Subject;
        returnedSummary.UserId.Should().Be(userId);
    }

    [Fact]
    public async Task GetByUserId_ShouldReturnOkWithUserSummaries()
    {
        // Arrange
        var userId = "test_user";
        var summaries = new List<CursorUserActivitySummaryDto>
        {
            new() { Id = 1, UserId = userId },
            new() { Id = 2, UserId = userId }
        };
        _mockService.Setup(x => x.GetByUserIdAsync(userId)).ReturnsAsync(summaries);

        // Act
        var result = await _controller.GetByUserId(userId);

        // Assert
        var okResult = result.Result.Should().BeOfType<OkObjectResult>().Subject;
        var returnedSummaries = okResult.Value.Should().BeAssignableTo<IEnumerable<CursorUserActivitySummaryDto>>().Subject;
        returnedSummaries.Should().HaveCount(2);
        returnedSummaries.Should().OnlyContain(x => x.UserId == userId);
    }

    [Fact]
    public async Task GetByDateRange_ShouldReturnOkWithSummariesInRange()
    {
        // Arrange
        var startDate = DateOnly.FromDateTime(DateTime.Today.AddDays(-5));
        var endDate = DateOnly.FromDateTime(DateTime.Today);
        var summaries = new List<CursorUserActivitySummaryDto>
        {
            new() { Id = 1, ReportDate = startDate },
            new() { Id = 2, ReportDate = endDate }
        };
        _mockService.Setup(x => x.GetByDateRangeAsync(startDate, endDate)).ReturnsAsync(summaries);

        // Act
        var result = await _controller.GetByDateRange(startDate, endDate);

        // Assert
        var okResult = result.Result.Should().BeOfType<OkObjectResult>().Subject;
        var returnedSummaries = okResult.Value.Should().BeAssignableTo<IEnumerable<CursorUserActivitySummaryDto>>().Subject;
        returnedSummaries.Should().HaveCount(2);
    }

    [Fact]
    public async Task GetActiveUsers_ShouldReturnOkWithActiveUsers()
    {
        // Arrange
        var reportDate = DateOnly.FromDateTime(DateTime.Today);
        var summaries = new List<CursorUserActivitySummaryDto>
        {
            new() { Id = 1, IsActive = true, ReportDate = reportDate },
            new() { Id = 2, IsActive = true, ReportDate = reportDate }
        };
        _mockService.Setup(x => x.GetActiveUsersAsync(reportDate)).ReturnsAsync(summaries);

        // Act
        var result = await _controller.GetActiveUsers(reportDate);

        // Assert
        var okResult = result.Result.Should().BeOfType<OkObjectResult>().Subject;
        var returnedSummaries = okResult.Value.Should().BeAssignableTo<IEnumerable<CursorUserActivitySummaryDto>>().Subject;
        returnedSummaries.Should().HaveCount(2);
        returnedSummaries.Should().OnlyContain(x => x.IsActive);
    }

    [Fact]
    public async Task GetPaged_ShouldReturnOkWithPagedData()
    {
        // Arrange
        var summaries = new List<CursorUserActivitySummaryDto>
        {
            new() { Id = 1 },
            new() { Id = 2 }
        };
        _mockService.Setup(x => x.GetPagedAsync(1, 10)).ReturnsAsync(summaries);
        _mockService.Setup(x => x.GetTotalCountAsync()).ReturnsAsync(20);

        // Act
        var result = await _controller.GetPaged(1, 10);

        // Assert
        var okResult = result.Result.Should().BeOfType<OkObjectResult>().Subject;
        var pagedData = okResult.Value.Should().BeAssignableTo<object>().Subject;
        
        // Verify the structure of the paged response
        var properties = pagedData.GetType().GetProperties();
        properties.Should().Contain(p => p.Name == "Data");
        properties.Should().Contain(p => p.Name == "Page");
        properties.Should().Contain(p => p.Name == "PageSize");
        properties.Should().Contain(p => p.Name == "TotalCount");
        properties.Should().Contain(p => p.Name == "TotalPages");
    }

    [Fact]
    public async Task GetPaged_ShouldReturnBadRequest_WhenInvalidParameters()
    {
        // Act
        var result = await _controller.GetPaged(0, 0);

        // Assert
        result.Result.Should().BeOfType<BadRequestObjectResult>();
    }

    [Fact]
    public async Task Create_ShouldReturnCreatedAtAction_WhenValidDto()
    {
        // Arrange
        var createDto = new CreateCursorUserActivitySummaryDto
        {
            UserId = "test_user",
            ReportDate = DateOnly.FromDateTime(DateTime.Today),
            IsActive = true
        };
        var createdSummary = new CursorUserActivitySummaryDto { Id = 1, UserId = "test_user" };
        
        _mockService.Setup(x => x.CreateAsync(createDto)).ReturnsAsync(createdSummary);

        // Act
        var result = await _controller.Create(createDto);

        // Assert
        var createdResult = result.Result.Should().BeOfType<CreatedAtActionResult>().Subject;
        createdResult.ActionName.Should().Be(nameof(_controller.GetById));
        createdResult.RouteValues!["id"].Should().Be(1);
        
        var returnedSummary = createdResult.Value.Should().BeOfType<CursorUserActivitySummaryDto>().Subject;
        returnedSummary.Id.Should().Be(1);
    }

    [Fact]
    public async Task Update_ShouldReturnOk_WhenSummaryExists()
    {
        // Arrange
        var updateDto = new UpdateCursorUserActivitySummaryDto
        {
            ChatSuggestedLinesAdded = 200
        };
        var updatedSummary = new CursorUserActivitySummaryDto { Id = 1, ChatSuggestedLinesAdded = 200 };
        
        _mockService.Setup(x => x.UpdateAsync(1, updateDto)).ReturnsAsync(updatedSummary);

        // Act
        var result = await _controller.Update(1, updateDto);

        // Assert
        var okResult = result.Result.Should().BeOfType<OkObjectResult>().Subject;
        var returnedSummary = okResult.Value.Should().BeOfType<CursorUserActivitySummaryDto>().Subject;
        returnedSummary.ChatSuggestedLinesAdded.Should().Be(200);
    }

    [Fact]
    public async Task Update_ShouldReturnNotFound_WhenSummaryDoesNotExist()
    {
        // Arrange
        var updateDto = new UpdateCursorUserActivitySummaryDto();
        _mockService.Setup(x => x.UpdateAsync(999, updateDto)).ReturnsAsync((CursorUserActivitySummaryDto?)null);

        // Act
        var result = await _controller.Update(999, updateDto);

        // Assert
        result.Result.Should().BeOfType<NotFoundObjectResult>();
    }

    [Fact]
    public async Task Delete_ShouldReturnNoContent_WhenSummaryExists()
    {
        // Arrange
        _mockService.Setup(x => x.DeleteAsync(1)).ReturnsAsync(true);

        // Act
        var result = await _controller.Delete(1);

        // Assert
        result.Should().BeOfType<NoContentResult>();
    }

    [Fact]
    public async Task Delete_ShouldReturnNotFound_WhenSummaryDoesNotExist()
    {
        // Arrange
        _mockService.Setup(x => x.DeleteAsync(999)).ReturnsAsync(false);

        // Act
        var result = await _controller.Delete(999);

        // Assert
        result.Should().BeOfType<NotFoundObjectResult>();
    }

    [Fact]
    public async Task Exists_ShouldReturnOk_WhenSummaryExists()
    {
        // Arrange
        _mockService.Setup(x => x.ExistsAsync(1)).ReturnsAsync(true);

        // Act
        var result = await _controller.Exists(1);

        // Assert
        result.Should().BeOfType<OkResult>();
    }

    [Fact]
    public async Task Exists_ShouldReturnNotFound_WhenSummaryDoesNotExist()
    {
        // Arrange
        _mockService.Setup(x => x.ExistsAsync(999)).ReturnsAsync(false);

        // Act
        var result = await _controller.Exists(999);

        // Assert
        result.Should().BeOfType<NotFoundResult>();
    }
}
