using FluentAssertions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using PerfStat.Jobs;
using PerfStat.Models;
using PerfStat.Services;
using Quartz;
using Xunit;

namespace PerfStat.Tests.Jobs;

public class DailyApiCallJobTests
{
    private readonly Mock<ICronJobService> _mockCronJobService;
    private readonly Mock<IOptions<CronJobSettings>> _mockOptions;
    private readonly Mock<ILogger<DailyApiCallJob>> _mockLogger;
    private readonly Mock<IJobExecutionContext> _mockContext;
    private readonly DailyApiCallJob _job;
    private readonly CronJobSettings _cronJobSettings;

    public DailyApiCallJobTests()
    {
        _mockCronJobService = new Mock<ICronJobService>();
        _mockOptions = new Mock<IOptions<CronJobSettings>>();
        _mockLogger = new Mock<ILogger<DailyApiCallJob>>();
        _mockContext = new Mock<IJobExecutionContext>();

        _cronJobSettings = new CronJobSettings
        {
            CronJobs = new Dictionary<string, CronJobConfig>
            {
                ["DailyApiCall"] = new CronJobConfig
                {
                    ApiUrl = "http://localhost:5141/api/testapi/daily-task",
                    HttpMethod = "POST",
                    Headers = new Dictionary<string, string>
                    {
                        ["Content-Type"] = "application/json"
                    },
                    RequestBody = "{\"source\":\"cron-job\",\"timestamp\":\"{{timestamp}}\"}"
                }
            }
        };

        _mockOptions.Setup(x => x.Value).Returns(_cronJobSettings);
        _job = new DailyApiCallJob(_mockCronJobService.Object, _mockOptions.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task Execute_ShouldCallCronJobService_WhenJobConfigExists()
    {
        // Arrange
        _mockCronJobService
            .Setup(x => x.ExecuteApiCallAsync(
                "DailyApiCall",
                "http://localhost:5141/api/testapi/daily-task",
                "POST",
                It.IsAny<Dictionary<string, string>>(),
                "{\"source\":\"cron-job\",\"timestamp\":\"{{timestamp}}\"}"))
            .Returns(Task.CompletedTask);

        // Act
        await _job.Execute(_mockContext.Object);

        // Assert
        _mockCronJobService.Verify(
            x => x.ExecuteApiCallAsync(
                "DailyApiCall",
                "http://localhost:5141/api/testapi/daily-task",
                "POST",
                It.IsAny<Dictionary<string, string>>(),
                "{\"source\":\"cron-job\",\"timestamp\":\"{{timestamp}}\"}"),
            Times.Once);
    }

    [Fact]
    public async Task Execute_ShouldLogError_WhenJobConfigNotFound()
    {
        // Arrange
        var emptySettings = new CronJobSettings { CronJobs = new Dictionary<string, CronJobConfig>() };
        _mockOptions.Setup(x => x.Value).Returns(emptySettings);
        
        var jobWithEmptySettings = new DailyApiCallJob(_mockCronJobService.Object, _mockOptions.Object, _mockLogger.Object);

        // Act
        await jobWithEmptySettings.Execute(_mockContext.Object);

        // Assert
        _mockCronJobService.Verify(
            x => x.ExecuteApiCallAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), 
                It.IsAny<Dictionary<string, string>>(), It.IsAny<string>()),
            Times.Never);

        // Verify error logging
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Job configuration not found for: DailyApiCall")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task Execute_ShouldLogStartAndCompletion()
    {
        // Arrange
        _mockCronJobService
            .Setup(x => x.ExecuteApiCallAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), 
                It.IsAny<Dictionary<string, string>>(), It.IsAny<string>()))
            .Returns(Task.CompletedTask);

        // Act
        await _job.Execute(_mockContext.Object);

        // Assert
        // Verify start logging
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Starting daily API call job")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);

        // Verify completion logging
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Daily API call job completed successfully")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task Execute_ShouldRethrowException_WhenCronJobServiceThrows()
    {
        // Arrange
        var expectedException = new Exception("Test exception");
        _mockCronJobService
            .Setup(x => x.ExecuteApiCallAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), 
                It.IsAny<Dictionary<string, string>>(), It.IsAny<string>()))
            .ThrowsAsync(expectedException);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<Exception>(() => _job.Execute(_mockContext.Object));
        exception.Should().Be(expectedException);

        // Verify error logging
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Error in daily API call job")),
                expectedException,
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task Execute_ShouldPassCorrectHeaders()
    {
        // Arrange
        Dictionary<string, string>? capturedHeaders = null;
        _mockCronJobService
            .Setup(x => x.ExecuteApiCallAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<Dictionary<string, string>>(),
                It.IsAny<string>()))
            .Callback<string, string, string, Dictionary<string, string>, string>(
                (jobName, apiUrl, httpMethod, headers, requestBody) =>
                {
                    capturedHeaders = headers;
                })
            .Returns(Task.CompletedTask);

        // Act
        await _job.Execute(_mockContext.Object);

        // Assert
        capturedHeaders.Should().NotBeNull();
        capturedHeaders!.Should().ContainKey("Content-Type");
        capturedHeaders["Content-Type"].Should().Be("application/json");
    }
}
