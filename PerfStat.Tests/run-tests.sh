#!/bin/bash

# 测试运行脚本
# 使用方法: ./run-tests.sh [test-type]
# test-type: unit, integration, e2e, all (默认: all)

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

TEST_TYPE=${1:-all}

echo -e "${BLUE}=== PerfStat 测试套件 ===${NC}"
echo "测试类型: $TEST_TYPE"
echo ""

# 函数：运行特定类型的测试
run_tests() {
    local filter=$1
    local description=$2
    
    echo -e "${YELLOW}运行 $description...${NC}"
    
    if [ -n "$filter" ]; then
        dotnet test PerfStat.Tests/PerfStat.Tests.csproj --filter "$filter" --verbosity normal --logger "console;verbosity=detailed"
    else
        dotnet test PerfStat.Tests/PerfStat.Tests.csproj --verbosity normal --logger "console;verbosity=detailed"
    fi
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ $description 通过${NC}"
    else
        echo -e "${RED}✗ $description 失败${NC}"
        exit 1
    fi
    echo ""
}

# 构建项目
echo -e "${YELLOW}构建项目...${NC}"
dotnet build PerfStat/PerfStat.csproj --configuration Debug
dotnet build PerfStat.Tests/PerfStat.Tests.csproj --configuration Debug

if [ $? -ne 0 ]; then
    echo -e "${RED}✗ 构建失败${NC}"
    exit 1
fi
echo -e "${GREEN}✓ 构建成功${NC}"
echo ""

# 根据参数运行不同类型的测试
case $TEST_TYPE in
    "unit")
        run_tests "Category!=Integration&Category!=E2E" "单元测试"
        ;;
    "integration")
        run_tests "Category=Integration" "集成测试"
        ;;
    "e2e")
        run_tests "Category=E2E" "E2E测试"
        ;;
    "all")
        echo -e "${BLUE}运行所有测试...${NC}"
        
        # 单元测试
        run_tests "TestCategory!=Integration&TestCategory!=E2E" "单元测试"
        
        # 集成测试
        run_tests "TestCategory=Integration" "集成测试"
        
        # E2E测试
        run_tests "TestCategory=E2E" "E2E测试"
        
        echo -e "${GREEN}=== 所有测试通过! ===${NC}"
        ;;
    *)
        echo -e "${RED}无效的测试类型: $TEST_TYPE${NC}"
        echo "支持的类型: unit, integration, e2e, all"
        exit 1
        ;;
esac

# 生成测试报告
echo -e "${YELLOW}生成测试报告...${NC}"
dotnet test PerfStat.Tests/PerfStat.Tests.csproj --collect:"XPlat Code Coverage" --results-directory ./TestResults

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ 测试报告已生成到 ./TestResults 目录${NC}"
else
    echo -e "${YELLOW}⚠ 测试报告生成失败，但测试已完成${NC}"
fi

echo ""
echo -e "${GREEN}=== 测试完成 ===${NC}"
