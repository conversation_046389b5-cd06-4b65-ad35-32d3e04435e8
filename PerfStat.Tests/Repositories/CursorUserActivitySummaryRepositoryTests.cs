using FluentAssertions;
using PerfStat.Repositories;
using Xunit;

namespace PerfStat.Tests.Repositories;

public class CursorUserActivitySummaryRepositoryTests : TestBase
{
    private readonly CursorUserActivitySummaryRepository _repository;

    public CursorUserActivitySummaryRepositoryTests()
    {
        _repository = new CursorUserActivitySummaryRepository(Context);
    }

    [Fact]
    public async Task AddAsync_ShouldAddEntitySuccessfully()
    {
        // Arrange
        var entity = TestDataFactory.CreateCursorUserActivitySummary();

        // Act
        var result = await _repository.AddAsync(entity);

        // Assert
        result.Should().NotBeNull();
        result.Id.Should().BeGreaterThan(0);
        result.UserId.Should().Be(entity.UserId);
        result.ReportDate.Should().Be(entity.ReportDate);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenEntityExists()
    {
        // Arrange
        var entity = TestDataFactory.CreateCursorUserActivitySummary();
        var addedEntity = await _repository.AddAsync(entity);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        result.Should().NotBeNull();
        result!.Id.Should().Be(addedEntity.Id);
        result.UserId.Should().Be(entity.UserId);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenEntityDoesNotExist()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetByUserIdAndDateAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var entity = TestDataFactory.CreateCursorUserActivitySummary();
        await _repository.AddAsync(entity);

        // Act
        var result = await _repository.GetByUserIdAndDateAsync(entity.UserId, entity.ReportDate);

        // Assert
        result.Should().NotBeNull();
        result!.UserId.Should().Be(entity.UserId);
        result.ReportDate.Should().Be(entity.ReportDate);
    }

    [Fact]
    public async Task GetByUserIdAsync_ShouldReturnUserEntities()
    {
        // Arrange
        var userId = "test_user";
        var entities = new[]
        {
            TestDataFactory.CreateCursorUserActivitySummary(userId, DateOnly.FromDateTime(DateTime.Today)),
            TestDataFactory.CreateCursorUserActivitySummary(userId, DateOnly.FromDateTime(DateTime.Today.AddDays(-1))),
            TestDataFactory.CreateCursorUserActivitySummary("other_user", DateOnly.FromDateTime(DateTime.Today))
        };

        foreach (var entity in entities)
        {
            await _repository.AddAsync(entity);
        }

        // Act
        var result = await _repository.GetByUserIdAsync(userId);

        // Assert
        result.Should().HaveCount(2);
        result.Should().OnlyContain(x => x.UserId == userId);
        result.Should().BeInDescendingOrder(x => x.ReportDate);
    }

    [Fact]
    public async Task GetByDateRangeAsync_ShouldReturnEntitiesInRange()
    {
        // Arrange
        var startDate = DateOnly.FromDateTime(DateTime.Today.AddDays(-5));
        var endDate = DateOnly.FromDateTime(DateTime.Today);
        
        var entities = new[]
        {
            TestDataFactory.CreateCursorUserActivitySummary("user1", startDate),
            TestDataFactory.CreateCursorUserActivitySummary("user2", DateOnly.FromDateTime(DateTime.Today.AddDays(-3))),
            TestDataFactory.CreateCursorUserActivitySummary("user3", endDate),
            TestDataFactory.CreateCursorUserActivitySummary("user4", DateOnly.FromDateTime(DateTime.Today.AddDays(-10))) // Outside range
        };

        foreach (var entity in entities)
        {
            await _repository.AddAsync(entity);
        }

        // Act
        var result = await _repository.GetByDateRangeAsync(startDate, endDate);

        // Assert
        result.Should().HaveCount(3);
        result.Should().OnlyContain(x => x.ReportDate >= startDate && x.ReportDate <= endDate);
        result.Should().BeInAscendingOrder(x => x.ReportDate);
    }

    [Fact]
    public async Task GetActiveUsersAsync_ShouldReturnOnlyActiveUsers()
    {
        // Arrange
        var reportDate = DateOnly.FromDateTime(DateTime.Today);
        var entities = new[]
        {
            TestDataFactory.CreateCursorUserActivitySummary("user1", reportDate, isActive: true),
            TestDataFactory.CreateCursorUserActivitySummary("user2", reportDate, isActive: false),
            TestDataFactory.CreateCursorUserActivitySummary("user3", reportDate, isActive: true),
            TestDataFactory.CreateCursorUserActivitySummary("user4", DateOnly.FromDateTime(DateTime.Today.AddDays(-1)), isActive: true) // Different date
        };

        foreach (var entity in entities)
        {
            await _repository.AddAsync(entity);
        }

        // Act
        var result = await _repository.GetActiveUsersAsync(reportDate);

        // Assert
        result.Should().HaveCount(2);
        result.Should().OnlyContain(x => x.IsActive && x.ReportDate == reportDate);
    }

    [Fact]
    public async Task GetByEmailAsync_ShouldReturnEntitiesWithMatchingEmail()
    {
        // Arrange
        var email = "<EMAIL>";
        var entities = new[]
        {
            TestDataFactory.CreateCursorUserActivitySummary("user1", email: email),
            TestDataFactory.CreateCursorUserActivitySummary("user2", email: "<EMAIL>"),
            TestDataFactory.CreateCursorUserActivitySummary("user3", email: null)
        };
        entities[2].WorkEmail = email; // Set work email

        foreach (var entity in entities)
        {
            await _repository.AddAsync(entity);
        }

        // Act
        var result = await _repository.GetByEmailAsync(email);

        // Assert
        result.Should().HaveCount(2);
        result.Should().Contain(x => x.Email == email);
        result.Should().Contain(x => x.WorkEmail == email);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntitySuccessfully()
    {
        // Arrange
        var entity = TestDataFactory.CreateCursorUserActivitySummary();
        var addedEntity = await _repository.AddAsync(entity);
        
        addedEntity.ChatSuggestedLinesAdded = 200;
        addedEntity.IsActive = false;

        // Act
        var result = await _repository.UpdateAsync(addedEntity);

        // Assert
        result.Should().NotBeNull();
        result.ChatSuggestedLinesAdded.Should().Be(200);
        result.IsActive.Should().BeFalse();
    }

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var entity = TestDataFactory.CreateCursorUserActivitySummary();
        var addedEntity = await _repository.AddAsync(entity);

        // Act
        await _repository.DeleteAsync(addedEntity.Id);

        // Assert
        var deletedEntity = await _repository.GetByIdAsync(addedEntity.Id);
        deletedEntity.Should().BeNull();
    }

    [Fact]
    public async Task ExistsAsync_ShouldReturnTrue_WhenEntityExists()
    {
        // Arrange
        var entity = TestDataFactory.CreateCursorUserActivitySummary();
        var addedEntity = await _repository.AddAsync(entity);

        // Act
        var result = await _repository.ExistsAsync(addedEntity.Id);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task ExistsAsync_ShouldReturnFalse_WhenEntityDoesNotExist()
    {
        // Act
        var result = await _repository.ExistsAsync(999);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task GetPagedAsync_ShouldReturnCorrectPage()
    {
        // Arrange
        var entities = TestDataFactory.CreateMultipleCursorUserActivitySummaries(10);
        foreach (var entity in entities)
        {
            await _repository.AddAsync(entity);
        }

        // Act
        var result = await _repository.GetPagedAsync(2, 3);

        // Assert
        result.Should().HaveCount(3);
    }

    [Fact]
    public async Task CountAsync_ShouldReturnCorrectCount()
    {
        // Arrange
        var entities = TestDataFactory.CreateMultipleCursorUserActivitySummaries(5);
        foreach (var entity in entities)
        {
            await _repository.AddAsync(entity);
        }

        // Act
        var result = await _repository.CountAsync();

        // Assert
        result.Should().Be(5);
    }
}
