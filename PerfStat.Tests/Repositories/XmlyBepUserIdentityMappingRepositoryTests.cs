using FluentAssertions;
using PerfStat.Repositories;
using Xunit;

namespace PerfStat.Tests.Repositories;

public class XmlyBepUserIdentityMappingRepositoryTests : TestBase
{
    private readonly XmlyBepUserIdentityMappingRepository _repository;

    public XmlyBepUserIdentityMappingRepositoryTests()
    {
        _repository = new XmlyBepUserIdentityMappingRepository(Context);
    }

    [Fact]
    public async Task AddAsync_ShouldAddEntitySuccessfully()
    {
        // Arrange
        var entity = TestDataFactory.CreateXmlyBepUserIdentityMapping();

        // Act
        var result = await _repository.AddAsync(entity);

        // Assert
        result.Should().NotBeNull();
        result.Id.Should().BeGreaterThan(0);
        result.Email.Should().Be(entity.Email);
        result.GitUserId.Should().Be(entity.GitUserId);
    }

    [Fact]
    public async Task GetByEmailAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var entity = TestDataFactory.CreateXmlyBepUserIdentityMapping();
        await _repository.AddAsync(entity);

        // Act
        var result = await _repository.GetByEmailAsync(entity.Email!);

        // Assert
        result.Should().NotBeNull();
        result!.Email.Should().Be(entity.Email);
    }

    [Fact]
    public async Task GetByEmailAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByEmailAsync("<EMAIL>");

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetByGitUserIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var entity = TestDataFactory.CreateXmlyBepUserIdentityMapping();
        await _repository.AddAsync(entity);

        // Act
        var result = await _repository.GetByGitUserIdAsync(entity.GitUserId!.Value);

        // Assert
        result.Should().NotBeNull();
        result!.GitUserId.Should().Be(entity.GitUserId);
    }

    [Fact]
    public async Task GetByUserCorpIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var entity = TestDataFactory.CreateXmlyBepUserIdentityMapping();
        await _repository.AddAsync(entity);

        // Act
        var result = await _repository.GetByUserCorpIdAsync(entity.UserCorpId!);

        // Assert
        result.Should().NotBeNull();
        result!.UserCorpId.Should().Be(entity.UserCorpId);
    }

    [Fact]
    public async Task GetByL1Async_ShouldReturnEntitiesWithMatchingL1()
    {
        // Arrange
        var l1 = "Engineering";
        var entities = new[]
        {
            TestDataFactory.CreateXmlyBepUserIdentityMapping("<EMAIL>"),
            TestDataFactory.CreateXmlyBepUserIdentityMapping("<EMAIL>"),
            TestDataFactory.CreateXmlyBepUserIdentityMapping("<EMAIL>")
        };
        entities[0].L1 = l1;
        entities[1].L1 = l1;
        entities[2].L1 = "Marketing";

        foreach (var entity in entities)
        {
            await _repository.AddAsync(entity);
        }

        // Act
        var result = await _repository.GetByL1Async(l1);

        // Assert
        result.Should().HaveCount(2);
        result.Should().OnlyContain(x => x.L1 == l1);
    }

    [Fact]
    public async Task GetByL2Async_ShouldReturnEntitiesWithMatchingL2()
    {
        // Arrange
        var l2 = "Backend";
        var entities = new[]
        {
            TestDataFactory.CreateXmlyBepUserIdentityMapping("<EMAIL>"),
            TestDataFactory.CreateXmlyBepUserIdentityMapping("<EMAIL>")
        };
        entities[0].L2 = l2;
        entities[1].L2 = "Frontend";

        foreach (var entity in entities)
        {
            await _repository.AddAsync(entity);
        }

        // Act
        var result = await _repository.GetByL2Async(l2);

        // Assert
        result.Should().HaveCount(1);
        result.First().L2.Should().Be(l2);
    }

    [Fact]
    public async Task GetByL3Async_ShouldReturnEntitiesWithMatchingL3()
    {
        // Arrange
        var l3 = "API Team";
        var entities = new[]
        {
            TestDataFactory.CreateXmlyBepUserIdentityMapping("<EMAIL>"),
            TestDataFactory.CreateXmlyBepUserIdentityMapping("<EMAIL>")
        };
        entities[0].L3 = l3;
        entities[1].L3 = "Database Team";

        foreach (var entity in entities)
        {
            await _repository.AddAsync(entity);
        }

        // Act
        var result = await _repository.GetByL3Async(l3);

        // Assert
        result.Should().HaveCount(1);
        result.First().L3.Should().Be(l3);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntitySuccessfully()
    {
        // Arrange
        var entity = TestDataFactory.CreateXmlyBepUserIdentityMapping();
        var addedEntity = await _repository.AddAsync(entity);
        
        addedEntity.L1 = "Updated Engineering";
        addedEntity.L2 = "Updated Backend";

        // Act
        var result = await _repository.UpdateAsync(addedEntity);

        // Assert
        result.Should().NotBeNull();
        result.L1.Should().Be("Updated Engineering");
        result.L2.Should().Be("Updated Backend");
    }

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var entity = TestDataFactory.CreateXmlyBepUserIdentityMapping();
        var addedEntity = await _repository.AddAsync(entity);

        // Act
        await _repository.DeleteAsync(addedEntity.Id);

        // Assert
        var deletedEntity = await _repository.GetByIdAsync(addedEntity.Id);
        deletedEntity.Should().BeNull();
    }

    [Fact]
    public async Task GetAllAsync_ShouldReturnAllEntities()
    {
        // Arrange
        var entities = TestDataFactory.CreateMultipleXmlyBepUserIdentityMappings(3);
        foreach (var entity in entities)
        {
            await _repository.AddAsync(entity);
        }

        // Act
        var result = await _repository.GetAllAsync();

        // Assert
        result.Should().HaveCount(3);
    }

    [Fact]
    public async Task GetPagedAsync_ShouldReturnCorrectPage()
    {
        // Arrange
        var entities = TestDataFactory.CreateMultipleXmlyBepUserIdentityMappings(10);
        foreach (var entity in entities)
        {
            await _repository.AddAsync(entity);
        }

        // Act
        var result = await _repository.GetPagedAsync(2, 3);

        // Assert
        result.Should().HaveCount(3);
    }

    [Fact]
    public async Task ExistsAsync_ShouldReturnTrue_WhenEntityExists()
    {
        // Arrange
        var entity = TestDataFactory.CreateXmlyBepUserIdentityMapping();
        var addedEntity = await _repository.AddAsync(entity);

        // Act
        var result = await _repository.ExistsAsync(addedEntity.Id);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task ExistsAsync_ShouldReturnFalse_WhenEntityDoesNotExist()
    {
        // Act
        var result = await _repository.ExistsAsync(999);

        // Assert
        result.Should().BeFalse();
    }
}
