using Microsoft.AspNetCore.Mvc;
using PerfStat.Models.DTOs;
using PerfStat.Services;

namespace PerfStat.Controllers;

[ApiController]
[Route("api/[controller]")]
public class CursorUserActivitySummaryController : ControllerBase
{
    private readonly ICursorUserActivitySummaryService _service;
    private readonly ILogger<CursorUserActivitySummaryController> _logger;

    public CursorUserActivitySummaryController(
        ICursorUserActivitySummaryService service,
        ILogger<CursorUserActivitySummaryController> logger)
    {
        _service = service;
        _logger = logger;
    }

    /// <summary>
    /// Get all cursor user activity summaries
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<CursorUserActivitySummaryDto>>> GetAll()
    {
        try
        {
            var summaries = await _service.GetAllAsync();
            return Ok(summaries);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving all cursor user activity summaries");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get cursor user activity summary by ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<CursorUserActivitySummaryDto>> GetById(int id)
    {
        try
        {
            var summary = await _service.GetByIdAsync(id);
            if (summary == null)
            {
                return NotFound($"Cursor user activity summary with ID {id} not found");
            }
            return Ok(summary);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving cursor user activity summary with ID: {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get cursor user activity summary by user ID and date
    /// </summary>
    [HttpGet("user/{userId}/date/{reportDate}")]
    public async Task<ActionResult<CursorUserActivitySummaryDto>> GetByUserIdAndDate(string userId, DateOnly reportDate)
    {
        try
        {
            var summary = await _service.GetByUserIdAndDateAsync(userId, reportDate);
            if (summary == null)
            {
                return NotFound($"Cursor user activity summary for user {userId} on {reportDate} not found");
            }
            return Ok(summary);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving cursor user activity summary for user {UserId} on {ReportDate}", userId, reportDate);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get all cursor user activity summaries for a specific user
    /// </summary>
    [HttpGet("user/{userId}")]
    public async Task<ActionResult<IEnumerable<CursorUserActivitySummaryDto>>> GetByUserId(string userId)
    {
        try
        {
            var summaries = await _service.GetByUserIdAsync(userId);
            return Ok(summaries);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving cursor user activity summaries for user: {UserId}", userId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get cursor user activity summaries by date range
    /// </summary>
    [HttpGet("date-range")]
    public async Task<ActionResult<IEnumerable<CursorUserActivitySummaryDto>>> GetByDateRange(
        [FromQuery] DateOnly startDate, 
        [FromQuery] DateOnly endDate)
    {
        try
        {
            var summaries = await _service.GetByDateRangeAsync(startDate, endDate);
            return Ok(summaries);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving cursor user activity summaries for date range {StartDate} to {EndDate}", startDate, endDate);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get active users for a specific date
    /// </summary>
    [HttpGet("active-users/{reportDate}")]
    public async Task<ActionResult<IEnumerable<CursorUserActivitySummaryDto>>> GetActiveUsers(DateOnly reportDate)
    {
        try
        {
            var summaries = await _service.GetActiveUsersAsync(reportDate);
            return Ok(summaries);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving active users for date: {ReportDate}", reportDate);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get cursor user activity summaries by email
    /// </summary>
    [HttpGet("email/{email}")]
    public async Task<ActionResult<IEnumerable<CursorUserActivitySummaryDto>>> GetByEmail(string email)
    {
        try
        {
            var summaries = await _service.GetByEmailAsync(email);
            return Ok(summaries);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving cursor user activity summaries for email: {Email}", email);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get paginated cursor user activity summaries
    /// </summary>
    [HttpGet("paged")]
    public async Task<ActionResult<object>> GetPaged([FromQuery] int page = 1, [FromQuery] int pageSize = 10)
    {
        try
        {
            if (page < 1 || pageSize < 1)
            {
                return BadRequest("Page and pageSize must be greater than 0");
            }

            var summaries = await _service.GetPagedAsync(page, pageSize);
            var totalCount = await _service.GetTotalCountAsync();

            return Ok(new
            {
                Data = summaries,
                Page = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving paged cursor user activity summaries");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Create a new cursor user activity summary
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<CursorUserActivitySummaryDto>> Create([FromBody] CreateCursorUserActivitySummaryDto createDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var summary = await _service.CreateAsync(createDto);
            return CreatedAtAction(nameof(GetById), new { id = summary.Id }, summary);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating cursor user activity summary");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Update an existing cursor user activity summary
    /// </summary>
    [HttpPut("{id}")]
    public async Task<ActionResult<CursorUserActivitySummaryDto>> Update(int id, [FromBody] UpdateCursorUserActivitySummaryDto updateDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var summary = await _service.UpdateAsync(id, updateDto);
            if (summary == null)
            {
                return NotFound($"Cursor user activity summary with ID {id} not found");
            }

            return Ok(summary);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating cursor user activity summary with ID: {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Delete a cursor user activity summary
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<ActionResult> Delete(int id)
    {
        try
        {
            var deleted = await _service.DeleteAsync(id);
            if (!deleted)
            {
                return NotFound($"Cursor user activity summary with ID {id} not found");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting cursor user activity summary with ID: {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Check if a cursor user activity summary exists
    /// </summary>
    [HttpHead("{id}")]
    public async Task<ActionResult> Exists(int id)
    {
        try
        {
            var exists = await _service.ExistsAsync(id);
            return exists ? Ok() : NotFound();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking existence of cursor user activity summary with ID: {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }
}
