using Microsoft.AspNetCore.Mvc;
using PerfStat.Models.DTOs;
using PerfStat.Services;

namespace PerfStat.Controllers;

[ApiController]
[Route("api/[controller]")]
public class XmlyBepUserIdentityMappingController : ControllerBase
{
    private readonly IXmlyBepUserIdentityMappingService _service;
    private readonly ILogger<XmlyBepUserIdentityMappingController> _logger;

    public XmlyBepUserIdentityMappingController(
        IXmlyBepUserIdentityMappingService service,
        ILogger<XmlyBepUserIdentityMappingController> logger)
    {
        _service = service;
        _logger = logger;
    }

    /// <summary>
    /// Get all user identity mappings
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<XmlyBepUserIdentityMappingDto>>> GetAll()
    {
        try
        {
            var mappings = await _service.GetAllAsync();
            return Ok(mappings);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving all user identity mappings");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get user identity mapping by ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<XmlyBepUserIdentityMappingDto>> GetById(int id)
    {
        try
        {
            var mapping = await _service.GetByIdAsync(id);
            if (mapping == null)
            {
                return NotFound($"User identity mapping with ID {id} not found");
            }
            return Ok(mapping);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving user identity mapping with ID: {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get user identity mapping by email
    /// </summary>
    [HttpGet("email/{email}")]
    public async Task<ActionResult<XmlyBepUserIdentityMappingDto>> GetByEmail(string email)
    {
        try
        {
            var mapping = await _service.GetByEmailAsync(email);
            if (mapping == null)
            {
                return NotFound($"User identity mapping with email {email} not found");
            }
            return Ok(mapping);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving user identity mapping for email: {Email}", email);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get user identity mapping by Git User ID
    /// </summary>
    [HttpGet("git-user/{gitUserId}")]
    public async Task<ActionResult<XmlyBepUserIdentityMappingDto>> GetByGitUserId(int gitUserId)
    {
        try
        {
            var mapping = await _service.GetByGitUserIdAsync(gitUserId);
            if (mapping == null)
            {
                return NotFound($"User identity mapping with Git User ID {gitUserId} not found");
            }
            return Ok(mapping);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving user identity mapping for Git User ID: {GitUserId}", gitUserId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get user identity mapping by User Corp ID
    /// </summary>
    [HttpGet("corp-id/{userCorpId}")]
    public async Task<ActionResult<XmlyBepUserIdentityMappingDto>> GetByUserCorpId(string userCorpId)
    {
        try
        {
            var mapping = await _service.GetByUserCorpIdAsync(userCorpId);
            if (mapping == null)
            {
                return NotFound($"User identity mapping with User Corp ID {userCorpId} not found");
            }
            return Ok(mapping);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving user identity mapping for User Corp ID: {UserCorpId}", userCorpId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get user identity mappings by L1
    /// </summary>
    [HttpGet("l1/{l1}")]
    public async Task<ActionResult<IEnumerable<XmlyBepUserIdentityMappingDto>>> GetByL1(string l1)
    {
        try
        {
            var mappings = await _service.GetByL1Async(l1);
            return Ok(mappings);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving user identity mappings for L1: {L1}", l1);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get user identity mappings by L2
    /// </summary>
    [HttpGet("l2/{l2}")]
    public async Task<ActionResult<IEnumerable<XmlyBepUserIdentityMappingDto>>> GetByL2(string l2)
    {
        try
        {
            var mappings = await _service.GetByL2Async(l2);
            return Ok(mappings);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving user identity mappings for L2: {L2}", l2);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get user identity mappings by L3
    /// </summary>
    [HttpGet("l3/{l3}")]
    public async Task<ActionResult<IEnumerable<XmlyBepUserIdentityMappingDto>>> GetByL3(string l3)
    {
        try
        {
            var mappings = await _service.GetByL3Async(l3);
            return Ok(mappings);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving user identity mappings for L3: {L3}", l3);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get paginated user identity mappings
    /// </summary>
    [HttpGet("paged")]
    public async Task<ActionResult<object>> GetPaged([FromQuery] int page = 1, [FromQuery] int pageSize = 10)
    {
        try
        {
            if (page < 1 || pageSize < 1)
            {
                return BadRequest("Page and pageSize must be greater than 0");
            }

            var mappings = await _service.GetPagedAsync(page, pageSize);
            var totalCount = await _service.GetTotalCountAsync();

            return Ok(new
            {
                Data = mappings,
                Page = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving paged user identity mappings");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Create a new user identity mapping
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<XmlyBepUserIdentityMappingDto>> Create([FromBody] CreateXmlyBepUserIdentityMappingDto createDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var mapping = await _service.CreateAsync(createDto);
            return CreatedAtAction(nameof(GetById), new { id = mapping.Id }, mapping);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating user identity mapping");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Update an existing user identity mapping
    /// </summary>
    [HttpPut("{id}")]
    public async Task<ActionResult<XmlyBepUserIdentityMappingDto>> Update(int id, [FromBody] UpdateXmlyBepUserIdentityMappingDto updateDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var mapping = await _service.UpdateAsync(id, updateDto);
            if (mapping == null)
            {
                return NotFound($"User identity mapping with ID {id} not found");
            }

            return Ok(mapping);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating user identity mapping with ID: {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Delete a user identity mapping
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<ActionResult> Delete(int id)
    {
        try
        {
            var deleted = await _service.DeleteAsync(id);
            if (!deleted)
            {
                return NotFound($"User identity mapping with ID {id} not found");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting user identity mapping with ID: {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Check if a user identity mapping exists
    /// </summary>
    [HttpHead("{id}")]
    public async Task<ActionResult> Exists(int id)
    {
        try
        {
            var exists = await _service.ExistsAsync(id);
            return exists ? Ok() : NotFound();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking existence of user identity mapping with ID: {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }
}
