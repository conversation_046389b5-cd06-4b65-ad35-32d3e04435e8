using Microsoft.AspNetCore.Mvc;

namespace PerfStat.Controllers;

[ApiController]
[Route("api/[controller]")]
public class TestApiController : ControllerBase
{
    private readonly ILogger<TestApiController> _logger;

    public TestApiController(ILogger<TestApiController> logger)
    {
        _logger = logger;
    }

    [HttpPost("daily-task")]
    public IActionResult DailyTask([FromBody] object requestData)
    {
        try
        {
            _logger.LogInformation("Daily task API called at {Timestamp} with data: {Data}", 
                DateTime.UtcNow, requestData?.ToString());

            return Ok(new
            {
                Message = "Daily task executed successfully",
                ReceivedAt = DateTime.UtcNow,
                Data = requestData,
                Status = "Success"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in daily task API");
            return StatusCode(500, new { Error = "Internal server error" });
        }
    }

    [HttpGet("health")]
    public IActionResult Health()
    {
        return Ok(new
        {
            Status = "Healthy",
            Timestamp = DateTime.UtcNow,
            Service = "TestApi"
        });
    }
}
