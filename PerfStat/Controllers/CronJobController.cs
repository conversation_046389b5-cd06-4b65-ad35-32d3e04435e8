using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using PerfStat.Models;
using PerfStat.Services;
using Quartz;

namespace PerfStat.Controllers;

[ApiController]
[Route("api/[controller]")]
public class CronJobController : ControllerBase
{
    private readonly IScheduler _scheduler;
    private readonly ICronJobService _cronJobService;
    private readonly CronJobSettings _cronJobSettings;
    private readonly ILogger<CronJobController> _logger;

    public CronJobController(
        IScheduler scheduler,
        ICronJobService cronJobService,
        IOptions<CronJobSettings> cronJobSettings,
        ILogger<CronJobController> logger)
    {
        _scheduler = scheduler;
        _cronJobService = cronJobService;
        _cronJobSettings = cronJobSettings.Value;
        _logger = logger;
    }

    [HttpGet("status")]
    public async Task<IActionResult> GetJobStatus()
    {
        try
        {
            var jobKeys = await _scheduler.GetJobKeys(Quartz.Impl.Matchers.GroupMatcher<JobKey>.AnyGroup());
            var jobStatuses = new List<object>();

            foreach (var jobKey in jobKeys)
            {
                var triggers = await _scheduler.GetTriggersOfJob(jobKey);
                var jobDetail = await _scheduler.GetJobDetail(jobKey);
                
                foreach (var trigger in triggers)
                {
                    var triggerState = await _scheduler.GetTriggerState(trigger.Key);
                    jobStatuses.Add(new
                    {
                        JobName = jobKey.Name,
                        JobGroup = jobKey.Group,
                        TriggerName = trigger.Key.Name,
                        TriggerState = triggerState.ToString(),
                        NextFireTime = trigger.GetNextFireTimeUtc()?.ToString("yyyy-MM-dd HH:mm:ss UTC"),
                        PreviousFireTime = trigger.GetPreviousFireTimeUtc()?.ToString("yyyy-MM-dd HH:mm:ss UTC"),
                        Description = jobDetail?.Description
                    });
                }
            }

            return Ok(new { Jobs = jobStatuses, Timestamp = DateTime.UtcNow });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting job status");
            return StatusCode(500, new { Error = "Failed to get job status" });
        }
    }

    [HttpPost("trigger/{jobName}")]
    public async Task<IActionResult> TriggerJob(string jobName)
    {
        try
        {
            if (!_cronJobSettings.CronJobs.ContainsKey(jobName))
            {
                return NotFound(new { Error = $"Job '{jobName}' not found in configuration" });
            }

            var jobConfig = _cronJobSettings.CronJobs[jobName];
            
            // 手动执行任务
            await _cronJobService.ExecuteApiCallAsync(
                jobName,
                jobConfig.ApiUrl,
                jobConfig.HttpMethod,
                jobConfig.Headers,
                jobConfig.RequestBody
            );

            _logger.LogInformation("Manually triggered job: {JobName}", jobName);
            return Ok(new { Message = $"Job '{jobName}' triggered successfully", Timestamp = DateTime.UtcNow });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error triggering job: {JobName}", jobName);
            return StatusCode(500, new { Error = $"Failed to trigger job '{jobName}'" });
        }
    }

    [HttpGet("config")]
    public IActionResult GetJobConfigurations()
    {
        try
        {
            return Ok(new { 
                CronJobs = _cronJobSettings.CronJobs,
                Timestamp = DateTime.UtcNow 
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting job configurations");
            return StatusCode(500, new { Error = "Failed to get job configurations" });
        }
    }
}
