using Microsoft.EntityFrameworkCore;
using PerfStat.Models;

namespace PerfStat.Data;

public class ApplicationDbContext : DbContext
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
    {
    }

    public DbSet<CursorUserActivitySummary> CursorUserActivitySummaries { get; set; }
    public DbSet<XmlyBepUserIdentityMapping> XmlyBepUserIdentityMappings { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure CursorUserActivitySummary
        modelBuilder.Entity<CursorUserActivitySummary>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => new { e.UserId, e.ReportDate })
                  .IsUnique()
                  .HasDatabaseName("uk_user_report_date");
            
            entity.Property(e => e.LastUpdated)
                  .HasDefaultValueSql("CURRENT_TIMESTAMP")
                  .ValueGeneratedOnAddOrUpdate();
        });

        // Configure XmlyBepUserIdentityMapping
        modelBuilder.Entity<XmlyBepUserIdentityMapping>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.Email)
                  .IsUnique()
                  .HasDatabaseName("uk_email");
        });
    }
}
