using Microsoft.Extensions.Options;
using PerfStat.Models;
using PerfStat.Services;
using Quartz;

namespace PerfStat.Jobs;

[DisallowConcurrentExecution]
public class DailyApiCallJob : IJob
{
    private readonly ICronJobService _cronJobService;
    private readonly CronJobSettings _cronJobSettings;
    private readonly ILogger<DailyApiCallJob> _logger;

    public DailyApiCallJob(
        ICronJobService cronJobService, 
        IOptions<CronJobSettings> cronJobSettings,
        ILogger<DailyApiCallJob> logger)
    {
        _cronJobService = cronJobService;
        _cronJobSettings = cronJobSettings.Value;
        _logger = logger;
    }

    public async Task Execute(IJobExecutionContext context)
    {
        const string jobName = "DailyApiCall";
        
        try
        {
            _logger.LogInformation("Starting daily API call job at {Timestamp}", DateTime.UtcNow);

            if (!_cronJobSettings.CronJobs.TryGetValue(jobName, out var jobConfig))
            {
                _logger.LogError("Job configuration not found for: {JobName}", jobName);
                return;
            }

            await _cronJobService.ExecuteApiCallAsync(
                jobName,
                jobConfig.ApiUrl,
                jobConfig.HttpMethod,
                jobConfig.Headers,
                jobConfig.RequestBody
            );

            _logger.LogInformation("Daily API call job completed successfully at {Timestamp}", DateTime.UtcNow);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in daily API call job");
            throw; // 重新抛出异常以便Quartz记录失败
        }
    }
}
