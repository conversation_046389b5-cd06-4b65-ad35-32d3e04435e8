#!/bin/bash

# MySQL CRUD API 测试脚本
# 使用方法: ./test-api.sh

BASE_URL="http://localhost:5141"

echo "=== MySQL CRUD API 测试脚本 ==="
echo "基础URL: $BASE_URL"
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试函数
test_endpoint() {
    local method=$1
    local url=$2
    local data=$3
    local description=$4
    
    echo -e "${BLUE}测试: $description${NC}"
    echo "请求: $method $url"
    
    if [ -n "$data" ]; then
        echo "数据: $data"
        response=$(curl -s -w "\n%{http_code}" -X $method "$BASE_URL$url" \
            -H "Content-Type: application/json" \
            -d "$data")
    else
        response=$(curl -s -w "\n%{http_code}" -X $method "$BASE_URL$url")
    fi
    
    # 分离响应体和状态码
    body=$(echo "$response" | head -n -1)
    status_code=$(echo "$response" | tail -n 1)
    
    if [[ $status_code -ge 200 && $status_code -lt 300 ]]; then
        echo -e "${GREEN}✓ 成功 (状态码: $status_code)${NC}"
        echo "响应: $body"
    else
        echo -e "${RED}✗ 失败 (状态码: $status_code)${NC}"
        echo "响应: $body"
    fi
    echo ""
}

echo "=== 1. 测试用户身份映射 API ==="

# 创建用户身份映射
test_endpoint "POST" "/api/XmlyBepUserIdentityMapping" \
'{
  "l1": "Engineering",
  "l2": "Backend",
  "l3": "API Team",
  "email": "<EMAIL>",
  "gitUserId": 12345,
  "userCorpId": "EMP001"
}' "创建用户身份映射"

# 获取所有用户身份映射
test_endpoint "GET" "/api/XmlyBepUserIdentityMapping" "" "获取所有用户身份映射"

# 根据邮箱查询
test_endpoint "GET" "/api/XmlyBepUserIdentityMapping/email/<EMAIL>" "" "根据邮箱查询用户身份映射"

# 根据Git用户ID查询
test_endpoint "GET" "/api/XmlyBepUserIdentityMapping/git-user/12345" "" "根据Git用户ID查询"

# 分页查询
test_endpoint "GET" "/api/XmlyBepUserIdentityMapping/paged?page=1&pageSize=5" "" "分页查询用户身份映射"

echo "=== 2. 测试用户活动汇总 API ==="

# 创建用户活动汇总
test_endpoint "POST" "/api/CursorUserActivitySummary" \
'{
  "reportDate": "2025-06-04",
  "userId": "test_user_123",
  "email": "<EMAIL>",
  "isActive": true,
  "chatSuggestedLinesAdded": 150,
  "chatSuggestedLinesDeleted": 30,
  "chatAcceptedLinesAdded": 120,
  "chatAcceptedLinesDeleted": 25,
  "chatTotalApplies": 75,
  "chatTotalAccepts": 60,
  "chatTotalRejects": 15,
  "chatTabsShown": 40,
  "tabsAccepted": 35,
  "editRequests": 50,
  "askRequests": 25,
  "agentRequests": 10,
  "cmdKUsages": 15,
  "subscriptionIncludedReqs": 200,
  "apiKeyReqs": 0,
  "usageBasedReqs": 0,
  "bugbotUsages": 3,
  "mostUsedModel": "gpt-4",
  "mostUsedApplyExtension": ".js",
  "mostUsedTabExtension": ".ts",
  "clientVersion": "1.2.0",
  "rawDate": "2025-06-04",
  "userName": "Test User",
  "workEmail": "<EMAIL>"
}' "创建用户活动汇总"

# 获取所有用户活动汇总
test_endpoint "GET" "/api/CursorUserActivitySummary" "" "获取所有用户活动汇总"

# 根据用户ID查询
test_endpoint "GET" "/api/CursorUserActivitySummary/user/test_user_123" "" "根据用户ID查询活动汇总"

# 根据用户ID和日期查询
test_endpoint "GET" "/api/CursorUserActivitySummary/user/test_user_123/date/2025-06-04" "" "根据用户ID和日期查询"

# 查询活跃用户
test_endpoint "GET" "/api/CursorUserActivitySummary/active-users/2025-06-04" "" "查询指定日期的活跃用户"

# 根据邮箱查询
test_endpoint "GET" "/api/CursorUserActivitySummary/email/<EMAIL>" "" "根据邮箱查询活动汇总"

# 日期范围查询
test_endpoint "GET" "/api/CursorUserActivitySummary/date-range?startDate=2025-06-01&endDate=2025-06-30" "" "日期范围查询"

# 分页查询
test_endpoint "GET" "/api/CursorUserActivitySummary/paged?page=1&pageSize=5" "" "分页查询用户活动汇总"

echo "=== 3. 测试更新操作 ==="

# 更新用户身份映射 (假设ID为1)
test_endpoint "PUT" "/api/XmlyBepUserIdentityMapping/1" \
'{
  "l1": "Engineering",
  "l2": "Frontend",
  "l3": "UI Team",
  "userCorpId": "EMP001_UPDATED"
}' "更新用户身份映射"

# 更新用户活动汇总 (假设ID为1)
test_endpoint "PUT" "/api/CursorUserActivitySummary/1" \
'{
  "chatSuggestedLinesAdded": 200,
  "chatAcceptedLinesAdded": 180,
  "editRequests": 75
}' "更新用户活动汇总"

echo "=== 4. 测试存在性检查 ==="

# 检查用户身份映射是否存在
test_endpoint "HEAD" "/api/XmlyBepUserIdentityMapping/1" "" "检查用户身份映射是否存在"

# 检查用户活动汇总是否存在
test_endpoint "HEAD" "/api/CursorUserActivitySummary/1" "" "检查用户活动汇总是否存在"

echo "=== 5. 测试错误情况 ==="

# 查询不存在的记录
test_endpoint "GET" "/api/XmlyBepUserIdentityMapping/99999" "" "查询不存在的用户身份映射"
test_endpoint "GET" "/api/CursorUserActivitySummary/99999" "" "查询不存在的用户活动汇总"

# 无效的分页参数
test_endpoint "GET" "/api/XmlyBepUserIdentityMapping/paged?page=0&pageSize=0" "" "无效的分页参数"

echo -e "${GREEN}=== 测试完成 ===${NC}"
echo "注意: 删除操作未包含在测试中，以避免删除测试数据"
echo "如需测试删除功能，请手动执行:"
echo "curl -X DELETE $BASE_URL/api/XmlyBepUserIdentityMapping/{id}"
echo "curl -X DELETE $BASE_URL/api/CursorUserActivitySummary/{id}"
