# MySQL CRUD API 文档

## 概述

本项目为远程MySQL数据库中的两个表提供了完整的CRUD WebAPI：
- `cursor_user_activity_summary` - 用户活动和AI辅助功能使用情况日度汇总表
- `xmlybep_user_identity_mapping` - 用户身份映射信息表

## 数据库配置

- **服务器**: 47.94.42.112:33306
- **数据库**: ad_other
- **用户名**: root
- **密码**: 3216732167

## 项目架构

```
PerfStat/
├── Controllers/                          # API控制器
│   ├── CursorUserActivitySummaryController.cs
│   └── XmlyBepUserIdentityMappingController.cs
├── Data/
│   └── ApplicationDbContext.cs           # EF Core数据库上下文
├── Models/
│   ├── CronJobSettings.cs               # 实体模型
│   └── DTOs/                            # 数据传输对象
│       ├── CursorUserActivitySummaryDto.cs
│       └── XmlyBepUserIdentityMappingDto.cs
├── Repositories/                        # 数据访问层
│   ├── IRepository.cs
│   ├── Repository.cs
│   ├── ICursorUserActivitySummaryRepository.cs
│   ├── CursorUserActivitySummaryRepository.cs
│   ├── IXmlyBepUserIdentityMappingRepository.cs
│   └── XmlyBepUserIdentityMappingRepository.cs
├── Services/                           # 业务逻辑层
│   ├── ICursorUserActivitySummaryService.cs
│   ├── CursorUserActivitySummaryService.cs
│   ├── IXmlyBepUserIdentityMappingService.cs
│   └── XmlyBepUserIdentityMappingService.cs
└── Mappings/
    └── AutoMapperProfile.cs            # 对象映射配置
```

## 技术栈

- **.NET 9.0**
- **Entity Framework Core 9.0.5**
- **Pomelo.EntityFrameworkCore.MySql 8.0.3** - MySQL提供程序
- **AutoMapper 12.0.1** - 对象映射
- **MySQL 数据库**

## API 端点

### 1. Cursor User Activity Summary API

基础路径: `/api/CursorUserActivitySummary`

#### 查询操作

| 方法 | 端点 | 描述 |
|------|------|------|
| GET | `/` | 获取所有记录 |
| GET | `/{id}` | 根据ID获取记录 |
| GET | `/user/{userId}` | 获取指定用户的所有记录 |
| GET | `/user/{userId}/date/{reportDate}` | 获取指定用户在指定日期的记录 |
| GET | `/date-range?startDate={start}&endDate={end}` | 获取日期范围内的记录 |
| GET | `/active-users/{reportDate}` | 获取指定日期的活跃用户 |
| GET | `/email/{email}` | 根据邮箱获取记录 |
| GET | `/paged?page={page}&pageSize={size}` | 分页获取记录 |

#### 修改操作

| 方法 | 端点 | 描述 |
|------|------|------|
| POST | `/` | 创建新记录 |
| PUT | `/{id}` | 更新指定记录 |
| DELETE | `/{id}` | 删除指定记录 |
| HEAD | `/{id}` | 检查记录是否存在 |

### 2. Xmly Bep User Identity Mapping API

基础路径: `/api/XmlyBepUserIdentityMapping`

#### 查询操作

| 方法 | 端点 | 描述 |
|------|------|------|
| GET | `/` | 获取所有记录 |
| GET | `/{id}` | 根据ID获取记录 |
| GET | `/email/{email}` | 根据邮箱获取记录 |
| GET | `/git-user/{gitUserId}` | 根据Git用户ID获取记录 |
| GET | `/corp-id/{userCorpId}` | 根据公司ID获取记录 |
| GET | `/l1/{l1}` | 根据L1获取记录 |
| GET | `/l2/{l2}` | 根据L2获取记录 |
| GET | `/l3/{l3}` | 根据L3获取记录 |
| GET | `/paged?page={page}&pageSize={size}` | 分页获取记录 |

#### 修改操作

| 方法 | 端点 | 描述 |
|------|------|------|
| POST | `/` | 创建新记录 |
| PUT | `/{id}` | 更新指定记录 |
| DELETE | `/{id}` | 删除指定记录 |
| HEAD | `/{id}` | 检查记录是否存在 |

## 数据模型

### CursorUserActivitySummary

```json
{
  "id": 1,
  "reportDate": "2025-06-04",
  "userId": "user123",
  "email": "<EMAIL>",
  "isActive": true,
  "chatSuggestedLinesAdded": 100,
  "chatSuggestedLinesDeleted": 20,
  "chatAcceptedLinesAdded": 80,
  "chatAcceptedLinesDeleted": 15,
  "chatTotalApplies": 50,
  "chatTotalAccepts": 40,
  "chatTotalRejects": 10,
  "chatTabsShown": 25,
  "tabsAccepted": 20,
  "editRequests": 30,
  "askRequests": 15,
  "agentRequests": 5,
  "cmdKUsages": 10,
  "subscriptionIncludedReqs": 100,
  "apiKeyReqs": 0,
  "usageBasedReqs": 0,
  "bugbotUsages": 2,
  "mostUsedModel": "gpt-4",
  "mostUsedApplyExtension": ".js",
  "mostUsedTabExtension": ".ts",
  "clientVersion": "1.0.0",
  "rawDate": "2025-06-04",
  "userName": "John Doe",
  "workEmail": "<EMAIL>",
  "lastUpdated": "2025-06-04T10:30:00Z"
}
```

### XmlyBepUserIdentityMapping

```json
{
  "id": 1,
  "l1": "Engineering",
  "l2": "Backend",
  "l3": "API Team",
  "email": "<EMAIL>",
  "gitUserId": 12345,
  "userCorpId": "EMP001"
}
```

## 使用示例

### 启动应用

```bash
cd PerfStat
dotnet run
```

应用将在 `http://localhost:5141` 启动。

### 创建用户活动记录

```bash
curl -X POST http://localhost:5141/api/CursorUserActivitySummary \
  -H "Content-Type: application/json" \
  -d '{
    "reportDate": "2025-06-04",
    "userId": "user123",
    "email": "<EMAIL>",
    "isActive": true,
    "chatSuggestedLinesAdded": 100,
    "chatAcceptedLinesAdded": 80,
    "editRequests": 30
  }'
```

### 查询用户活动记录

```bash
# 获取所有记录
curl http://localhost:5141/api/CursorUserActivitySummary

# 根据用户ID查询
curl http://localhost:5141/api/CursorUserActivitySummary/user/user123

# 分页查询
curl "http://localhost:5141/api/CursorUserActivitySummary/paged?page=1&pageSize=10"
```

### 创建用户身份映射

```bash
curl -X POST http://localhost:5141/api/XmlyBepUserIdentityMapping \
  -H "Content-Type: application/json" \
  -d '{
    "l1": "Engineering",
    "l2": "Backend",
    "l3": "API Team",
    "email": "<EMAIL>",
    "gitUserId": 12345,
    "userCorpId": "EMP001"
  }'
```

### 查询用户身份映射

```bash
# 根据邮箱查询
curl http://localhost:5141/api/XmlyBepUserIdentityMapping/email/<EMAIL>

# 根据Git用户ID查询
curl http://localhost:5141/api/XmlyBepUserIdentityMapping/git-user/12345
```

## 错误处理

API返回标准HTTP状态码：

- **200 OK**: 请求成功
- **201 Created**: 资源创建成功
- **204 No Content**: 删除成功
- **400 Bad Request**: 请求参数错误
- **404 Not Found**: 资源不存在
- **500 Internal Server Error**: 服务器内部错误

错误响应格式：
```json
{
  "error": "错误描述信息"
}
```

## 分页响应格式

```json
{
  "data": [...],
  "page": 1,
  "pageSize": 10,
  "totalCount": 100,
  "totalPages": 10
}
```

## 注意事项

1. **数据库连接**: 确保网络能够访问远程MySQL服务器
2. **时区处理**: 日期字段使用 `DateOnly` 类型，时间戳使用 `DateTime`
3. **唯一约束**: 
   - `cursor_user_activity_summary` 表有 `(User_ID, report_date)` 唯一约束
   - `xmlybep_user_identity_mapping` 表有 `email` 唯一约束
4. **自动时间戳**: `last_updated` 字段会自动更新
5. **空值处理**: 大部分字段允许为空，创建时会使用默认值

## 扩展功能

可以考虑添加：
- 批量操作API
- 数据导入/导出功能
- 统计分析API
- 数据验证规则
- API版本控制
- 缓存机制
