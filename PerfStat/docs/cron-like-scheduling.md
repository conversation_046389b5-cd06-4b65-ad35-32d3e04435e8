# Cron-like Scheduling 功能文档

## 概述

本项目集成了基于 Quartz.NET 的类似 crontab 的定时任务调度功能，支持 GMT+8 时区配置，可以定时执行 HTTP API 调用。

## 项目结构

```
PerfStat/
├── Controllers/
│   ├── CronJobController.cs          # 新增：定时任务管理API
│   └── TestApiController.cs          # 新增：测试API端点
├── Jobs/
│   └── DailyApiCallJob.cs           # 新增：每日API调用任务
├── Models/
│   └── CronJobSettings.cs           # 新增：配置模型
├── Services/
│   ├── ICronJobService.cs           # 新增：服务接口
│   └── CronJobService.cs            # 新增：服务实现
├── docs/
│   └── cron-like-scheduling.md      # 新增：功能文档
├── Program.cs                       # 修改：添加Quartz配置
├── appsettings.json                 # 修改：添加定时任务配置
└── PerfStat.csproj                  # 修改：添加NuGet包引用
```

## 新增文件和修改点

### 新增文件

#### 1. 配置模型 (`Models/CronJobSettings.cs`)
```csharp
public class CronJobSettings
{
    public Dictionary<string, CronJobConfig> CronJobs { get; set; } = new();
}

public class CronJobConfig
{
    public string CronExpression { get; set; } = string.Empty;
    public string TimeZone { get; set; } = "UTC";
    public string ApiUrl { get; set; } = string.Empty;
    public string HttpMethod { get; set; } = "GET";
    public Dictionary<string, string> Headers { get; set; } = new();
    public string RequestBody { get; set; } = string.Empty;
}
```

#### 2. 服务接口 (`Services/ICronJobService.cs`)
定义了定时任务服务的接口，用于执行 HTTP API 调用。

#### 3. 服务实现 (`Services/CronJobService.cs`)
实现了定时任务的核心逻辑：
- HTTP 请求发送
- 请求体占位符替换（如 `{{timestamp}}`）
- 错误处理和日志记录

#### 4. 定时任务 Job (`Jobs/DailyApiCallJob.cs`)
具体的定时任务实现，每天上午 10 点（GMT+8）执行：
- 使用 `[DisallowConcurrentExecution]` 防止并发执行
- 集成配置系统和日志记录

#### 5. 管理控制器 (`Controllers/CronJobController.cs`)
提供 REST API 用于管理定时任务：
- 查看任务状态
- 手动触发任务
- 查看任务配置

#### 6. 测试控制器 (`Controllers/TestApiController.cs`)
提供测试端点，用于验证定时任务功能。

### 修改的文件

#### 1. `Program.cs`
添加了以下配置：
```csharp
// 配置CronJob设置
builder.Services.Configure<CronJobSettings>(builder.Configuration);

// 注册HTTP客户端和服务
builder.Services.AddHttpClient<ICronJobService, CronJobService>();
builder.Services.AddScoped<ICronJobService, CronJobService>();

// 配置Quartz调度器
builder.Services.AddQuartz(q =>
{
    q.UseInMemoryStore();
    
    var dailyJobKey = new JobKey("DailyApiCallJob");
    q.AddJob<DailyApiCallJob>(opts => opts.WithIdentity(dailyJobKey));
    
    q.AddTrigger(opts => opts
        .ForJob(dailyJobKey)
        .WithIdentity("DailyApiCallTrigger")
        .WithCronSchedule("0 0 10 * * ?", 
            x => x.InTimeZone(TimeZoneInfo.FindSystemTimeZoneById("Asia/Shanghai")))
        .StartNow());
});

// 添加Quartz托管服务
builder.Services.AddQuartzHostedService(q => q.WaitForJobsToComplete = true);

// 注册控制器支持
builder.Services.AddControllers();
app.MapControllers();
```

#### 2. `appsettings.json`
添加了定时任务配置：
```json
{
  "CronJobs": {
    "DailyApiCall": {
      "CronExpression": "0 0 10 * * ?",
      "TimeZone": "Asia/Shanghai",
      "ApiUrl": "http://localhost:5141/api/testapi/daily-task",
      "HttpMethod": "POST",
      "Headers": {
        "Content-Type": "application/json",
        "User-Agent": "PerfStat-CronJob/1.0"
      },
      "RequestBody": "{\"source\":\"cron-job\",\"timestamp\":\"{{timestamp}}\"}"
    }
  }
}
```

#### 3. `PerfStat.csproj`
添加了必要的 NuGet 包：
- `Quartz` (3.14.0)
- `Quartz.Extensions.Hosting` (3.14.0)
- `Microsoft.Extensions.Http` (9.0.5)

## 配置说明

### Cron 表达式格式
格式：`秒 分 时 日 月 星期`

常用示例：
- `0 0 10 * * ?` - 每天上午 10 点
- `0 30 9 * * MON-FRI` - 工作日上午 9:30
- `0 0 12 1 * ?` - 每月 1 号中午 12 点
- `0 0/15 * * * ?` - 每 15 分钟执行一次
- `0 0 0 * * SUN` - 每周日午夜

### 时区配置
支持标准时区标识符：
- `Asia/Shanghai` - 中国标准时间 (GMT+8)
- `UTC` - 协调世界时
- `America/New_York` - 美国东部时间
- `Europe/London` - 英国时间

### HTTP 配置选项
- **ApiUrl**: 目标 API 地址
- **HttpMethod**: HTTP 方法 (GET, POST, PUT, DELETE 等)
- **Headers**: 自定义请求头
- **RequestBody**: 请求体内容，支持占位符替换

### 占位符支持
- `{{timestamp}}` - Unix 时间戳

## API 接口

### 1. 查看任务状态
```http
GET /api/cronjob/status
```

响应示例：
```json
{
  "jobs": [
    {
      "jobName": "DailyApiCallJob",
      "jobGroup": "DEFAULT",
      "triggerName": "DailyApiCallTrigger",
      "triggerState": "Normal",
      "nextFireTime": "2025-06-05 02:00:00 UTC",
      "previousFireTime": null,
      "description": null
    }
  ],
  "timestamp": "2025-06-04T10:20:16.300923Z"
}
```

### 2. 手动触发任务
```http
POST /api/cronjob/trigger/{jobName}
```

示例：
```bash
curl -X POST http://localhost:5141/api/cronjob/trigger/DailyApiCall
```

响应：
```json
{
  "message": "Job 'DailyApiCall' triggered successfully",
  "timestamp": "2025-06-04T10:24:09.529861Z"
}
```

### 3. 查看任务配置
```http
GET /api/cronjob/config
```

### 4. 测试 API
```http
POST /api/testapi/daily-task
GET /api/testapi/health
```

## 使用指南

### 启动应用
```bash
cd PerfStat
dotnet run
```

### 验证功能
1. **查看任务状态**：
   ```bash
   curl http://localhost:5141/api/cronjob/status
   ```

2. **手动触发任务**：
   ```bash
   curl -X POST http://localhost:5141/api/cronjob/trigger/DailyApiCall
   ```

3. **查看配置**：
   ```bash
   curl http://localhost:5141/api/cronjob/config
   ```

### 添加新任务

1. **在 `appsettings.json` 中添加配置**：
```json
{
  "CronJobs": {
    "WeeklyReport": {
      "CronExpression": "0 0 9 * * MON",
      "TimeZone": "Asia/Shanghai",
      "ApiUrl": "https://api.example.com/weekly-report",
      "HttpMethod": "POST",
      "Headers": {
        "Authorization": "Bearer your-token",
        "Content-Type": "application/json"
      },
      "RequestBody": "{\"type\":\"weekly\",\"timestamp\":\"{{timestamp}}\"}"
    }
  }
}
```

2. **创建专用 Job 类**（可选）：
```csharp
[DisallowConcurrentExecution]
public class WeeklyReportJob : IJob
{
    private readonly ICronJobService _cronJobService;
    private readonly CronJobSettings _settings;
    
    public async Task Execute(IJobExecutionContext context)
    {
        // 实现具体逻辑
    }
}
```

3. **在 `Program.cs` 中注册**：
```csharp
var weeklyJobKey = new JobKey("WeeklyReportJob");
q.AddJob<WeeklyReportJob>(opts => opts.WithIdentity(weeklyJobKey));
q.AddTrigger(opts => opts
    .ForJob(weeklyJobKey)
    .WithIdentity("WeeklyReportTrigger")
    .WithCronSchedule("0 0 9 * * MON", 
        x => x.InTimeZone(TimeZoneInfo.FindSystemTimeZoneById("Asia/Shanghai")))
    .StartNow());
```

## 日志和监控

### 日志级别
- **Information**: 任务开始和完成
- **Warning**: HTTP 请求失败
- **Error**: 任务执行异常

### 日志示例
```
info: PerfStat.Services.CronJobService[0]
      Executing cron job: DailyApiCall at 06/04/2025 10:22:08
info: PerfStat.Services.CronJobService[0]
      Cron job DailyApiCall completed successfully. Response: {"status":"success"}
```

## 注意事项

1. **时区设置**: 确保服务器时区正确或在配置中明确指定
2. **网络访问**: 确保应用能访问目标 API
3. **SSL 证书**: HTTPS 调用需要有效的 SSL 证书
4. **并发控制**: 使用 `[DisallowConcurrentExecution]` 防止任务重复执行
5. **错误处理**: 任务失败不会影响后续执行，但会记录日志
6. **内存存储**: 当前使用内存存储，重启后任务历史会丢失

## 故障排除

| 问题 | 可能原因 | 解决方案 |
|------|----------|----------|
| 任务未执行 | Cron 表达式错误 | 检查表达式格式和时区设置 |
| API 调用失败 | 网络或目标服务问题 | 检查网络连接和目标 API 状态 |
| 配置不生效 | JSON 格式错误 | 验证 appsettings.json 格式 |
| 服务启动失败 | 依赖注入问题 | 检查 Program.cs 中的服务注册 |

## 测试验证

### 完整测试流程

1. **启动应用**：
   ```bash
   cd PerfStat
   dotnet run
   ```

2. **验证任务状态**：
   ```bash
   curl http://localhost:5141/api/cronjob/status
   ```

   预期响应：
   ```json
   {
     "jobs": [
       {
         "jobName": "DailyApiCallJob",
         "triggerState": "Normal",
         "nextFireTime": "2025-06-05 02:00:00 UTC"
       }
     ]
   }
   ```

3. **手动触发任务**：
   ```bash
   curl -X POST http://localhost:5141/api/cronjob/trigger/DailyApiCall
   ```

4. **验证测试 API**：
   ```bash
   curl http://localhost:5141/api/testapi/health
   ```

### 日志验证
成功执行后应看到类似日志：
```
info: PerfStat.Services.CronJobService[0]
      Executing cron job: DailyApiCall at 06/04/2025 10:24:09
info: PerfStat.Controllers.TestApiController[0]
      Daily task API called at 06/04/2025 10:24:09 with data: {"source":"cron-job","timestamp":"**********"}
info: PerfStat.Services.CronJobService[0]
      Cron job DailyApiCall completed successfully.
```

## 扩展功能

未来可以考虑添加：
- 持久化存储（数据库）
- 任务执行历史记录
- 邮件通知功能
- Web 管理界面
- 任务依赖关系
- 动态任务配置

## 总结

本功能成功实现了：
✅ 基于 Quartz.NET 的定时任务调度
✅ GMT+8 时区支持
✅ 每天上午 10 点自动执行
✅ HTTP API 调用功能
✅ 配置化管理
✅ REST API 管理接口
✅ 完整的日志记录
✅ 手动触发功能
✅ 占位符替换支持
