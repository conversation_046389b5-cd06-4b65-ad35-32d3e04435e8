# MySQL CRUD API 快速启动指南

## 🚀 快速开始

### 1. 启动应用
```bash
cd PerfStat
dotnet run
```

应用将在 `http://localhost:5141` 启动，并自动连接到远程MySQL数据库。

### 2. 验证API功能

#### 测试用户身份映射API

**创建记录**：
```bash
curl -X POST http://localhost:5141/api/XmlyBepUserIdentityMapping \
  -H "Content-Type: application/json" \
  -d '{
    "l1": "Engineering",
    "l2": "Backend", 
    "l3": "API Team",
    "email": "<EMAIL>",
    "gitUserId": 12345,
    "userCorpId": "EMP001"
  }'
```

**查询所有记录**：
```bash
curl http://localhost:5141/api/XmlyBepUserIdentityMapping
```

**根据邮箱查询**：
```bash
curl http://localhost:5141/api/XmlyBepUserIdentityMapping/email/<EMAIL>
```

#### 测试用户活动汇总API

**创建记录**：
```bash
curl -X POST http://localhost:5141/api/CursorUserActivitySummary \
  -H "Content-Type: application/json" \
  -d '{
    "reportDate": "2025-06-04",
    "userId": "test_user_123",
    "email": "<EMAIL>",
    "isActive": true,
    "chatSuggestedLinesAdded": 150,
    "chatAcceptedLinesAdded": 120,
    "editRequests": 50
  }'
```

**查询用户活动**：
```bash
curl http://localhost:5141/api/CursorUserActivitySummary/user/test_user_123
```

**分页查询**：
```bash
curl "http://localhost:5141/api/CursorUserActivitySummary/paged?page=1&pageSize=5"
```

### 3. 运行完整测试

使用提供的测试脚本：
```bash
./PerfStat/scripts/test-api.sh
```

## 📊 数据库信息

- **服务器**: ************:33306
- **数据库**: ad_other
- **表**:
  - `cursor_user_activity_summary` - 用户活动汇总
  - `xmlybep_user_identity_mapping` - 用户身份映射

## 🔧 主要功能

### 用户身份映射 (`/api/XmlyBepUserIdentityMapping`)
- ✅ CRUD操作（创建、读取、更新、删除）
- ✅ 根据邮箱、Git用户ID、公司ID查询
- ✅ 按组织层级（L1、L2、L3）查询
- ✅ 分页查询

### 用户活动汇总 (`/api/CursorUserActivitySummary`)
- ✅ CRUD操作
- ✅ 根据用户ID、日期、邮箱查询
- ✅ 日期范围查询
- ✅ 活跃用户查询
- ✅ 分页查询

## 📋 响应格式

### 成功响应
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "isActive": true,
  "lastUpdated": "2025-06-05T18:14:09"
}
```

### 分页响应
```json
{
  "data": [...],
  "page": 1,
  "pageSize": 5,
  "totalCount": 10,
  "totalPages": 2
}
```

### 错误响应
```json
{
  "error": "Record not found"
}
```

## 🛠️ 技术栈

- **.NET 9.0**
- **Entity Framework Core 9.0.5**
- **Pomelo.EntityFrameworkCore.MySql 9.0.0-preview.3**
- **AutoMapper 12.0.1**
- **MySQL 数据库**

## 📚 更多信息

详细API文档请参考：[mysql-crud-api.md](./mysql-crud-api.md)

## ⚠️ 注意事项

1. **网络连接**: 确保能访问远程MySQL服务器
2. **唯一约束**: 
   - 用户活动表：`(User_ID, report_date)` 唯一
   - 身份映射表：`email` 唯一
3. **时间戳**: `last_updated` 字段自动更新
4. **默认值**: 数值字段有默认值0，布尔字段默认false

## 🔍 故障排除

如果遇到连接问题：
1. 检查网络连接
2. 验证数据库凭据
3. 确认防火墙设置
4. 查看应用日志
