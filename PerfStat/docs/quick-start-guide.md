# Cron-like Scheduling 快速启动指南

## 🚀 快速开始

### 1. 启动应用
```bash
cd PerfStat
dotnet run
```

应用将在 `http://localhost:5141` 启动。

### 2. 验证定时任务功能

#### 查看任务状态
```bash
curl http://localhost:5141/api/cronjob/status
```

#### 手动触发任务
```bash
curl -X POST http://localhost:5141/api/cronjob/trigger/DailyApiCall
```

#### 查看任务配置
```bash
curl http://localhost:5141/api/cronjob/config
```

#### 测试健康检查
```bash
curl http://localhost:5141/api/testapi/health
```

## 📋 默认配置

- **执行时间**: 每天上午 10:00 (GMT+8)
- **目标API**: `http://localhost:5141/api/testapi/daily-task`
- **请求方法**: POST
- **请求体**: `{"source":"cron-job","timestamp":"{{timestamp}}"}`

## 🔧 自定义配置

编辑 `appsettings.json` 中的 `CronJobs` 部分：

```json
{
  "CronJobs": {
    "DailyApiCall": {
      "CronExpression": "0 0 10 * * ?",
      "TimeZone": "Asia/Shanghai",
      "ApiUrl": "http://your-api.com/endpoint",
      "HttpMethod": "POST",
      "Headers": {
        "Content-Type": "application/json",
        "Authorization": "Bearer your-token"
      },
      "RequestBody": "{\"data\":\"your-data\"}"
    }
  }
}
```

## 📊 监控和日志

启动后查看控制台日志，成功执行时会显示：

```
info: PerfStat.Services.CronJobService[0]
      Executing cron job: DailyApiCall at 06/04/2025 10:24:09
info: PerfStat.Services.CronJobService[0]
      Cron job DailyApiCall completed successfully.
```

## 🛠️ 常用 Cron 表达式

| 表达式 | 说明 |
|--------|------|
| `0 0 10 * * ?` | 每天上午10点 |
| `0 30 9 * * MON-FRI` | 工作日上午9:30 |
| `0 0 12 1 * ?` | 每月1号中午12点 |
| `0 0/15 * * * ?` | 每15分钟 |
| `0 0 0 * * SUN` | 每周日午夜 |

## 📚 更多信息

详细文档请参考：[cron-like-scheduling.md](./cron-like-scheduling.md)
