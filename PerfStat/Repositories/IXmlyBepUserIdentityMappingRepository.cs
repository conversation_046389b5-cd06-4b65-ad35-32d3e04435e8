using PerfStat.Models;

namespace PerfStat.Repositories;

public interface IXmlyBepUserIdentityMappingRepository : IRepository<XmlyBepUserIdentityMapping>
{
    Task<XmlyBepUserIdentityMapping?> GetByEmailAsync(string email);
    Task<XmlyBepUserIdentityMapping?> GetByGitUserIdAsync(int gitUserId);
    Task<XmlyBepUserIdentityMapping?> GetByUserCorpIdAsync(string userCorpId);
    Task<IEnumerable<XmlyBepUserIdentityMapping>> GetByL1Async(string l1);
    Task<IEnumerable<XmlyBepUserIdentityMapping>> GetByL2Async(string l2);
    Task<IEnumerable<XmlyBepUserIdentityMapping>> GetByL3Async(string l3);
}
