using PerfStat.Models;

namespace PerfStat.Repositories;

public interface ICursorUserActivitySummaryRepository : IRepository<CursorUserActivitySummary>
{
    Task<CursorUserActivitySummary?> GetByUserIdAndDateAsync(string userId, DateOnly reportDate);
    Task<IEnumerable<CursorUserActivitySummary>> GetByUserIdAsync(string userId);
    Task<IEnumerable<CursorUserActivitySummary>> GetByDateRangeAsync(DateOnly startDate, DateOnly endDate);
    Task<IEnumerable<CursorUserActivitySummary>> GetActiveUsersAsync(DateOnly reportDate);
    Task<IEnumerable<CursorUserActivitySummary>> GetByEmailAsync(string email);
}
