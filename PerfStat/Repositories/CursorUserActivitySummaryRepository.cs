using Microsoft.EntityFrameworkCore;
using PerfStat.Data;
using PerfStat.Models;

namespace PerfStat.Repositories;

public class CursorUserActivitySummaryRepository : Repository<CursorUserActivitySummary>, ICursorUserActivitySummaryRepository
{
    public CursorUserActivitySummaryRepository(ApplicationDbContext context) : base(context)
    {
    }

    public async Task<CursorUserActivitySummary?> GetByUserIdAndDateAsync(string userId, DateOnly reportDate)
    {
        return await _dbSet.FirstOrDefaultAsync(x => x.UserId == userId && x.ReportDate == reportDate);
    }

    public async Task<IEnumerable<CursorUserActivitySummary>> GetByUserIdAsync(string userId)
    {
        return await _dbSet.Where(x => x.UserId == userId).OrderByDescending(x => x.ReportDate).ToListAsync();
    }

    public async Task<IEnumerable<CursorUserActivitySummary>> GetByDateRangeAsync(DateOnly startDate, DateOnly endDate)
    {
        return await _dbSet.Where(x => x.ReportDate >= startDate && x.ReportDate <= endDate)
                          .OrderBy(x => x.ReportDate)
                          .ToListAsync();
    }

    public async Task<IEnumerable<CursorUserActivitySummary>> GetActiveUsersAsync(DateOnly reportDate)
    {
        return await _dbSet.Where(x => x.ReportDate == reportDate && x.IsActive)
                          .ToListAsync();
    }

    public async Task<IEnumerable<CursorUserActivitySummary>> GetByEmailAsync(string email)
    {
        return await _dbSet.Where(x => x.Email == email || x.WorkEmail == email)
                          .OrderByDescending(x => x.ReportDate)
                          .ToListAsync();
    }
}
