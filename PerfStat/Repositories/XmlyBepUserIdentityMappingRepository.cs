using Microsoft.EntityFrameworkCore;
using PerfStat.Data;
using PerfStat.Models;

namespace PerfStat.Repositories;

public class XmlyBepUserIdentityMappingRepository : Repository<XmlyBepUserIdentityMapping>, IXmlyBepUserIdentityMappingRepository
{
    public XmlyBepUserIdentityMappingRepository(ApplicationDbContext context) : base(context)
    {
    }

    public async Task<XmlyBepUserIdentityMapping?> GetByEmailAsync(string email)
    {
        return await _dbSet.FirstOrDefaultAsync(x => x.Email == email);
    }

    public async Task<XmlyBepUserIdentityMapping?> GetByGitUserIdAsync(int gitUserId)
    {
        return await _dbSet.FirstOrDefaultAsync(x => x.GitUserId == gitUserId);
    }

    public async Task<XmlyBepUserIdentityMapping?> GetByUserCorpIdAsync(string userCorpId)
    {
        return await _dbSet.FirstOrDefaultAsync(x => x.UserCorpId == userCorpId);
    }

    public async Task<IEnumerable<XmlyBepUserIdentityMapping>> GetByL1Async(string l1)
    {
        return await _dbSet.Where(x => x.L1 == l1).ToListAsync();
    }

    public async Task<IEnumerable<XmlyBepUserIdentityMapping>> GetByL2Async(string l2)
    {
        return await _dbSet.Where(x => x.L2 == l2).ToListAsync();
    }

    public async Task<IEnumerable<XmlyBepUserIdentityMapping>> GetByL3Async(string l3)
    {
        return await _dbSet.Where(x => x.L3 == l3).ToListAsync();
    }
}
