using AutoMapper;
using PerfStat.Models;
using PerfStat.Models.DTOs;
using PerfStat.Repositories;

namespace PerfStat.Services;

public class XmlyBepUserIdentityMappingService : IXmlyBepUserIdentityMappingService
{
    private readonly IXmlyBepUserIdentityMappingRepository _repository;
    private readonly IMapper _mapper;
    private readonly ILogger<XmlyBepUserIdentityMappingService> _logger;

    public XmlyBepUserIdentityMappingService(
        IXmlyBepUserIdentityMappingRepository repository,
        IMapper mapper,
        ILogger<XmlyBepUserIdentityMappingService> logger)
    {
        _repository = repository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<XmlyBepUserIdentityMappingDto?> GetByIdAsync(int id)
    {
        var entity = await _repository.GetByIdAsync(id);
        return entity == null ? null : _mapper.Map<XmlyBepUserIdentityMappingDto>(entity);
    }

    public async Task<IEnumerable<XmlyBepUserIdentityMappingDto>> GetAllAsync()
    {
        var entities = await _repository.GetAllAsync();
        return _mapper.Map<IEnumerable<XmlyBepUserIdentityMappingDto>>(entities);
    }

    public async Task<XmlyBepUserIdentityMappingDto?> GetByEmailAsync(string email)
    {
        var entity = await _repository.GetByEmailAsync(email);
        return entity == null ? null : _mapper.Map<XmlyBepUserIdentityMappingDto>(entity);
    }

    public async Task<XmlyBepUserIdentityMappingDto?> GetByGitUserIdAsync(int gitUserId)
    {
        var entity = await _repository.GetByGitUserIdAsync(gitUserId);
        return entity == null ? null : _mapper.Map<XmlyBepUserIdentityMappingDto>(entity);
    }

    public async Task<XmlyBepUserIdentityMappingDto?> GetByUserCorpIdAsync(string userCorpId)
    {
        var entity = await _repository.GetByUserCorpIdAsync(userCorpId);
        return entity == null ? null : _mapper.Map<XmlyBepUserIdentityMappingDto>(entity);
    }

    public async Task<IEnumerable<XmlyBepUserIdentityMappingDto>> GetByL1Async(string l1)
    {
        var entities = await _repository.GetByL1Async(l1);
        return _mapper.Map<IEnumerable<XmlyBepUserIdentityMappingDto>>(entities);
    }

    public async Task<IEnumerable<XmlyBepUserIdentityMappingDto>> GetByL2Async(string l2)
    {
        var entities = await _repository.GetByL2Async(l2);
        return _mapper.Map<IEnumerable<XmlyBepUserIdentityMappingDto>>(entities);
    }

    public async Task<IEnumerable<XmlyBepUserIdentityMappingDto>> GetByL3Async(string l3)
    {
        var entities = await _repository.GetByL3Async(l3);
        return _mapper.Map<IEnumerable<XmlyBepUserIdentityMappingDto>>(entities);
    }

    public async Task<XmlyBepUserIdentityMappingDto> CreateAsync(CreateXmlyBepUserIdentityMappingDto createDto)
    {
        try
        {
            var entity = _mapper.Map<XmlyBepUserIdentityMapping>(createDto);
            var createdEntity = await _repository.AddAsync(entity);
            _logger.LogInformation("Created XmlyBepUserIdentityMapping with ID: {Id}", createdEntity.Id);
            return _mapper.Map<XmlyBepUserIdentityMappingDto>(createdEntity);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating XmlyBepUserIdentityMapping for email {Email}", createDto.Email);
            throw;
        }
    }

    public async Task<XmlyBepUserIdentityMappingDto?> UpdateAsync(int id, UpdateXmlyBepUserIdentityMappingDto updateDto)
    {
        try
        {
            var existingEntity = await _repository.GetByIdAsync(id);
            if (existingEntity == null)
            {
                return null;
            }

            _mapper.Map(updateDto, existingEntity);
            var updatedEntity = await _repository.UpdateAsync(existingEntity);
            _logger.LogInformation("Updated XmlyBepUserIdentityMapping with ID: {Id}", id);
            return _mapper.Map<XmlyBepUserIdentityMappingDto>(updatedEntity);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating XmlyBepUserIdentityMapping with ID: {Id}", id);
            throw;
        }
    }

    public async Task<bool> DeleteAsync(int id)
    {
        try
        {
            var exists = await _repository.ExistsAsync(id);
            if (!exists)
            {
                return false;
            }

            await _repository.DeleteAsync(id);
            _logger.LogInformation("Deleted XmlyBepUserIdentityMapping with ID: {Id}", id);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting XmlyBepUserIdentityMapping with ID: {Id}", id);
            throw;
        }
    }

    public async Task<bool> ExistsAsync(int id)
    {
        return await _repository.ExistsAsync(id);
    }

    public async Task<int> GetTotalCountAsync()
    {
        return await _repository.CountAsync();
    }

    public async Task<IEnumerable<XmlyBepUserIdentityMappingDto>> GetPagedAsync(int page, int pageSize)
    {
        var entities = await _repository.GetPagedAsync(page, pageSize);
        return _mapper.Map<IEnumerable<XmlyBepUserIdentityMappingDto>>(entities);
    }
}
