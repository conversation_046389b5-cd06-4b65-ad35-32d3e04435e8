using AutoMapper;
using PerfStat.Models;
using PerfStat.Models.DTOs;
using PerfStat.Repositories;

namespace PerfStat.Services;

public class CursorUserActivitySummaryService : ICursorUserActivitySummaryService
{
    private readonly ICursorUserActivitySummaryRepository _repository;
    private readonly IMapper _mapper;
    private readonly ILogger<CursorUserActivitySummaryService> _logger;

    public CursorUserActivitySummaryService(
        ICursorUserActivitySummaryRepository repository,
        IMapper mapper,
        ILogger<CursorUserActivitySummaryService> logger)
    {
        _repository = repository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<CursorUserActivitySummaryDto?> GetByIdAsync(int id)
    {
        var entity = await _repository.GetByIdAsync(id);
        return entity == null ? null : _mapper.Map<CursorUserActivitySummaryDto>(entity);
    }

    public async Task<IEnumerable<CursorUserActivitySummaryDto>> GetAllAsync()
    {
        var entities = await _repository.GetAllAsync();
        return _mapper.Map<IEnumerable<CursorUserActivitySummaryDto>>(entities);
    }

    public async Task<CursorUserActivitySummaryDto?> GetByUserIdAndDateAsync(string userId, DateOnly reportDate)
    {
        var entity = await _repository.GetByUserIdAndDateAsync(userId, reportDate);
        return entity == null ? null : _mapper.Map<CursorUserActivitySummaryDto>(entity);
    }

    public async Task<IEnumerable<CursorUserActivitySummaryDto>> GetByUserIdAsync(string userId)
    {
        var entities = await _repository.GetByUserIdAsync(userId);
        return _mapper.Map<IEnumerable<CursorUserActivitySummaryDto>>(entities);
    }

    public async Task<IEnumerable<CursorUserActivitySummaryDto>> GetByDateRangeAsync(DateOnly startDate, DateOnly endDate)
    {
        var entities = await _repository.GetByDateRangeAsync(startDate, endDate);
        return _mapper.Map<IEnumerable<CursorUserActivitySummaryDto>>(entities);
    }

    public async Task<IEnumerable<CursorUserActivitySummaryDto>> GetActiveUsersAsync(DateOnly reportDate)
    {
        var entities = await _repository.GetActiveUsersAsync(reportDate);
        return _mapper.Map<IEnumerable<CursorUserActivitySummaryDto>>(entities);
    }

    public async Task<IEnumerable<CursorUserActivitySummaryDto>> GetByEmailAsync(string email)
    {
        var entities = await _repository.GetByEmailAsync(email);
        return _mapper.Map<IEnumerable<CursorUserActivitySummaryDto>>(entities);
    }

    public async Task<CursorUserActivitySummaryDto> CreateAsync(CreateCursorUserActivitySummaryDto createDto)
    {
        try
        {
            var entity = _mapper.Map<CursorUserActivitySummary>(createDto);
            var createdEntity = await _repository.AddAsync(entity);
            _logger.LogInformation("Created CursorUserActivitySummary with ID: {Id}", createdEntity.Id);
            return _mapper.Map<CursorUserActivitySummaryDto>(createdEntity);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating CursorUserActivitySummary for user {UserId} on {ReportDate}", 
                createDto.UserId, createDto.ReportDate);
            throw;
        }
    }

    public async Task<CursorUserActivitySummaryDto?> UpdateAsync(int id, UpdateCursorUserActivitySummaryDto updateDto)
    {
        try
        {
            var existingEntity = await _repository.GetByIdAsync(id);
            if (existingEntity == null)
            {
                return null;
            }

            _mapper.Map(updateDto, existingEntity);
            var updatedEntity = await _repository.UpdateAsync(existingEntity);
            _logger.LogInformation("Updated CursorUserActivitySummary with ID: {Id}", id);
            return _mapper.Map<CursorUserActivitySummaryDto>(updatedEntity);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating CursorUserActivitySummary with ID: {Id}", id);
            throw;
        }
    }

    public async Task<bool> DeleteAsync(int id)
    {
        try
        {
            var exists = await _repository.ExistsAsync(id);
            if (!exists)
            {
                return false;
            }

            await _repository.DeleteAsync(id);
            _logger.LogInformation("Deleted CursorUserActivitySummary with ID: {Id}", id);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting CursorUserActivitySummary with ID: {Id}", id);
            throw;
        }
    }

    public async Task<bool> ExistsAsync(int id)
    {
        return await _repository.ExistsAsync(id);
    }

    public async Task<int> GetTotalCountAsync()
    {
        return await _repository.CountAsync();
    }

    public async Task<IEnumerable<CursorUserActivitySummaryDto>> GetPagedAsync(int page, int pageSize)
    {
        var entities = await _repository.GetPagedAsync(page, pageSize);
        return _mapper.Map<IEnumerable<CursorUserActivitySummaryDto>>(entities);
    }
}
