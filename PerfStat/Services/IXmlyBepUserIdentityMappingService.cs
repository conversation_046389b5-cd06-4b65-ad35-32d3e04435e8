using PerfStat.Models.DTOs;

namespace PerfStat.Services;

public interface IXmlyBepUserIdentityMappingService
{
    Task<XmlyBepUserIdentityMappingDto?> GetByIdAsync(int id);
    Task<IEnumerable<XmlyBepUserIdentityMappingDto>> GetAllAsync();
    Task<XmlyBepUserIdentityMappingDto?> GetByEmailAsync(string email);
    Task<XmlyBepUserIdentityMappingDto?> GetByGitUserIdAsync(int gitUserId);
    Task<XmlyBepUserIdentityMappingDto?> GetByUserCorpIdAsync(string userCorpId);
    Task<IEnumerable<XmlyBepUserIdentityMappingDto>> GetByL1Async(string l1);
    Task<IEnumerable<XmlyBepUserIdentityMappingDto>> GetByL2Async(string l2);
    Task<IEnumerable<XmlyBepUserIdentityMappingDto>> GetByL3Async(string l3);
    Task<XmlyBepUserIdentityMappingDto> CreateAsync(CreateXmlyBepUserIdentityMappingDto createDto);
    Task<XmlyBepUserIdentityMappingDto?> UpdateAsync(int id, UpdateXmlyBepUserIdentityMappingDto updateDto);
    Task<bool> DeleteAsync(int id);
    Task<bool> ExistsAsync(int id);
    Task<int> GetTotalCountAsync();
    Task<IEnumerable<XmlyBepUserIdentityMappingDto>> GetPagedAsync(int page, int pageSize);
}
