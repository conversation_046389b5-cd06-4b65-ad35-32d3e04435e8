using PerfStat.Models.DTOs;

namespace PerfStat.Services;

public interface ICursorUserActivitySummaryService
{
    Task<CursorUserActivitySummaryDto?> GetByIdAsync(int id);
    Task<IEnumerable<CursorUserActivitySummaryDto>> GetAllAsync();
    Task<CursorUserActivitySummaryDto?> GetByUserIdAndDateAsync(string userId, DateOnly reportDate);
    Task<IEnumerable<CursorUserActivitySummaryDto>> GetByUserIdAsync(string userId);
    Task<IEnumerable<CursorUserActivitySummaryDto>> GetByDateRangeAsync(DateOnly startDate, DateOnly endDate);
    Task<IEnumerable<CursorUserActivitySummaryDto>> GetActiveUsersAsync(DateOnly reportDate);
    Task<IEnumerable<CursorUserActivitySummaryDto>> GetByEmailAsync(string email);
    Task<CursorUserActivitySummaryDto> CreateAsync(CreateCursorUserActivitySummaryDto createDto);
    Task<CursorUserActivitySummaryDto?> UpdateAsync(int id, UpdateCursorUserActivitySummaryDto updateDto);
    Task<bool> DeleteAsync(int id);
    Task<bool> ExistsAsync(int id);
    Task<int> GetTotalCountAsync();
    Task<IEnumerable<CursorUserActivitySummaryDto>> GetPagedAsync(int page, int pageSize);
}
