using System.Text;

namespace PerfStat.Services;

public class CronJobService : ICronJobService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<CronJobService> _logger;

    public CronJobService(HttpClient httpClient, ILogger<CronJobService> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
    }

    public async Task ExecuteApiCallAsync(string jobName, string apiUrl, string httpMethod, 
        Dictionary<string, string> headers, string requestBody)
    {
        try
        {
            _logger.LogInformation("Executing cron job: {JobName} at {Timestamp}", 
                jobName, DateTime.UtcNow);

            // 替换请求体中的占位符
            var processedBody = ProcessRequestBody(requestBody);

            // 创建HTTP请求
            var request = new HttpRequestMessage(new HttpMethod(httpMethod), apiUrl);

            // 添加请求头
            foreach (var header in headers)
            {
                if (header.Key.Equals("Content-Type", StringComparison.OrdinalIgnoreCase))
                {
                    continue; // Content-Type 会在设置内容时自动添加
                }
                request.Headers.Add(header.Key, header.Value);
            }

            // 添加请求体（如果有）
            if (!string.IsNullOrEmpty(processedBody) && 
                (httpMethod.Equals("POST", StringComparison.OrdinalIgnoreCase) || 
                 httpMethod.Equals("PUT", StringComparison.OrdinalIgnoreCase) ||
                 httpMethod.Equals("PATCH", StringComparison.OrdinalIgnoreCase)))
            {
                var contentType = headers.GetValueOrDefault("Content-Type", "application/json");
                request.Content = new StringContent(processedBody, Encoding.UTF8, contentType);
            }

            // 发送请求
            var response = await _httpClient.SendAsync(request);

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                _logger.LogInformation("Cron job {JobName} completed successfully. Response: {Response}", 
                    jobName, responseContent);
            }
            else
            {
                _logger.LogWarning("Cron job {JobName} failed with status code: {StatusCode}", 
                    jobName, response.StatusCode);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing cron job: {JobName}", jobName);
        }
    }

    private static string ProcessRequestBody(string requestBody)
    {
        if (string.IsNullOrEmpty(requestBody))
            return requestBody;

        // 替换时间戳占位符
        var timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
        return requestBody.Replace("{{timestamp}}", timestamp.ToString());
    }
}
