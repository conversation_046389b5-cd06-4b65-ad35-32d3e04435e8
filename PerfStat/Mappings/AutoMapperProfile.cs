using AutoMapper;
using PerfStat.Models;
using PerfStat.Models.DTOs;

namespace PerfStat.Mappings;

public class AutoMapperProfile : Profile
{
    public AutoMapperProfile()
    {
        // CursorUserActivitySummary mappings
        CreateMap<CursorUserActivitySummary, CursorUserActivitySummaryDto>();
        CreateMap<CreateCursorUserActivitySummaryDto, CursorUserActivitySummary>();
        CreateMap<UpdateCursorUserActivitySummaryDto, CursorUserActivitySummary>()
            .ForAllMembers(opts => opts.Condition((src, dest, srcMember) => srcMember != null));

        // XmlyBepUserIdentityMapping mappings
        CreateMap<XmlyBepUserIdentityMapping, XmlyBepUserIdentityMappingDto>();
        CreateMap<CreateXmlyBepUserIdentityMappingDto, XmlyBepUserIdentityMapping>();
        CreateMap<UpdateXmlyBepUserIdentityMappingDto, XmlyBepUserIdentityMapping>()
            .ForAllMembers(opts => opts.Condition((src, dest, srcMember) => srcMember != null));
    }
}
