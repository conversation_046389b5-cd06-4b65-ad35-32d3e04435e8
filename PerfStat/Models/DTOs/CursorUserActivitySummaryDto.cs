namespace PerfStat.Models.DTOs;

public class CursorUserActivitySummaryDto
{
    public int Id { get; set; }
    public DateOnly ReportDate { get; set; }
    public string UserId { get; set; } = string.Empty;
    public string? Email { get; set; }
    public bool IsActive { get; set; }
    public int ChatSuggestedLinesAdded { get; set; }
    public int ChatSuggestedLinesDeleted { get; set; }
    public int ChatAcceptedLinesAdded { get; set; }
    public int ChatAcceptedLinesDeleted { get; set; }
    public int ChatTotalApplies { get; set; }
    public int ChatTotalAccepts { get; set; }
    public int ChatTotalRejects { get; set; }
    public int ChatTabsShown { get; set; }
    public int TabsAccepted { get; set; }
    public int EditRequests { get; set; }
    public int AskRequests { get; set; }
    public int AgentRequests { get; set; }
    public int CmdKUsages { get; set; }
    public int SubscriptionIncludedReqs { get; set; }
    public int ApiKeyReqs { get; set; }
    public int UsageBasedReqs { get; set; }
    public int BugbotUsages { get; set; }
    public string? MostUsedModel { get; set; }
    public string? MostUsedApplyExtension { get; set; }
    public string? MostUsedTabExtension { get; set; }
    public string? ClientVersion { get; set; }
    public string? RawDate { get; set; }
    public string? UserName { get; set; }
    public string? WorkEmail { get; set; }
    public DateTime LastUpdated { get; set; }
}

public class CreateCursorUserActivitySummaryDto
{
    public DateOnly ReportDate { get; set; }
    public string UserId { get; set; } = string.Empty;
    public string? Email { get; set; }
    public bool IsActive { get; set; } = false;
    public int ChatSuggestedLinesAdded { get; set; } = 0;
    public int ChatSuggestedLinesDeleted { get; set; } = 0;
    public int ChatAcceptedLinesAdded { get; set; } = 0;
    public int ChatAcceptedLinesDeleted { get; set; } = 0;
    public int ChatTotalApplies { get; set; } = 0;
    public int ChatTotalAccepts { get; set; } = 0;
    public int ChatTotalRejects { get; set; } = 0;
    public int ChatTabsShown { get; set; } = 0;
    public int TabsAccepted { get; set; } = 0;
    public int EditRequests { get; set; } = 0;
    public int AskRequests { get; set; } = 0;
    public int AgentRequests { get; set; } = 0;
    public int CmdKUsages { get; set; } = 0;
    public int SubscriptionIncludedReqs { get; set; } = 0;
    public int ApiKeyReqs { get; set; } = 0;
    public int UsageBasedReqs { get; set; } = 0;
    public int BugbotUsages { get; set; } = 0;
    public string? MostUsedModel { get; set; }
    public string? MostUsedApplyExtension { get; set; }
    public string? MostUsedTabExtension { get; set; }
    public string? ClientVersion { get; set; }
    public string? RawDate { get; set; }
    public string? UserName { get; set; }
    public string? WorkEmail { get; set; }
}

public class UpdateCursorUserActivitySummaryDto
{
    public string? Email { get; set; }
    public bool? IsActive { get; set; }
    public int? ChatSuggestedLinesAdded { get; set; }
    public int? ChatSuggestedLinesDeleted { get; set; }
    public int? ChatAcceptedLinesAdded { get; set; }
    public int? ChatAcceptedLinesDeleted { get; set; }
    public int? ChatTotalApplies { get; set; }
    public int? ChatTotalAccepts { get; set; }
    public int? ChatTotalRejects { get; set; }
    public int? ChatTabsShown { get; set; }
    public int? TabsAccepted { get; set; }
    public int? EditRequests { get; set; }
    public int? AskRequests { get; set; }
    public int? AgentRequests { get; set; }
    public int? CmdKUsages { get; set; }
    public int? SubscriptionIncludedReqs { get; set; }
    public int? ApiKeyReqs { get; set; }
    public int? UsageBasedReqs { get; set; }
    public int? BugbotUsages { get; set; }
    public string? MostUsedModel { get; set; }
    public string? MostUsedApplyExtension { get; set; }
    public string? MostUsedTabExtension { get; set; }
    public string? ClientVersion { get; set; }
    public string? RawDate { get; set; }
    public string? UserName { get; set; }
    public string? WorkEmail { get; set; }
}
