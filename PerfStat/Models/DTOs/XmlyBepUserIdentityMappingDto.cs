namespace PerfStat.Models.DTOs;

public class XmlyBepUserIdentityMappingDto
{
    public int Id { get; set; }
    public string? L1 { get; set; }
    public string? L2 { get; set; }
    public string? L3 { get; set; }
    public string? Email { get; set; }
    public int? GitUserId { get; set; }
    public string? UserCorpId { get; set; }
}

public class CreateXmlyBepUserIdentityMappingDto
{
    public string? L1 { get; set; }
    public string? L2 { get; set; }
    public string? L3 { get; set; }
    public string? Email { get; set; }
    public int? GitUserId { get; set; }
    public string? UserCorpId { get; set; }
}

public class UpdateXmlyBepUserIdentityMappingDto
{
    public string? L1 { get; set; }
    public string? L2 { get; set; }
    public string? L3 { get; set; }
    public string? Email { get; set; }
    public int? GitUserId { get; set; }
    public string? UserCorpId { get; set; }
}
