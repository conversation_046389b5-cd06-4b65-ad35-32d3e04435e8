namespace PerfStat.Models;

public class CronJobSettings
{
    public Dictionary<string, CronJobConfig> CronJobs { get; set; } = new();
}

public class CronJobConfig
{
    public string CronExpression { get; set; } = string.Empty;
    public string TimeZone { get; set; } = "UTC";
    public string ApiUrl { get; set; } = string.Empty;
    public string HttpMethod { get; set; } = "GET";
    public Dictionary<string, string> Headers { get; set; } = new();
    public string RequestBody { get; set; } = string.Empty;
}
