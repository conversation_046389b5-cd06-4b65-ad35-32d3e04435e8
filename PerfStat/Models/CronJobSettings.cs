using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PerfStat.Models;

// Cron Job Settings
public class CronJobSettings
{
    public Dictionary<string, CronJobConfig> CronJobs { get; set; } = new();
}

public class CronJobConfig
{
    public string CronExpression { get; set; } = string.Empty;
    public string TimeZone { get; set; } = "UTC";
    public string ApiUrl { get; set; } = string.Empty;
    public string HttpMethod { get; set; } = "GET";
    public Dictionary<string, string> Headers { get; set; } = new();
    public string RequestBody { get; set; } = string.Empty;
}

// Database Entities
[Table("cursor_user_activity_summary")]
public class CursorUserActivitySummary
{
    [Key]
    [Column("id")]
    public int Id { get; set; }

    [Required]
    [Column("report_date")]
    public DateOnly ReportDate { get; set; }

    [Required]
    [Column("User_ID")]
    [StringLength(255)]
    public string UserId { get; set; } = string.Empty;

    [Column("Email")]
    [StringLength(255)]
    public string? Email { get; set; }

    [Column("Is_Active")]
    public bool IsActive { get; set; } = false;

    [Column("Chat_Suggested_Lines_Added")]
    public int ChatSuggestedLinesAdded { get; set; } = 0;

    [Column("Chat_Suggested_Lines_Deleted")]
    public int ChatSuggestedLinesDeleted { get; set; } = 0;

    [Column("Chat_Accepted_Lines_Added")]
    public int ChatAcceptedLinesAdded { get; set; } = 0;

    [Column("Chat_Accepted_Lines_Deleted")]
    public int ChatAcceptedLinesDeleted { get; set; } = 0;

    [Column("Chat_Total_Applies")]
    public int ChatTotalApplies { get; set; } = 0;

    [Column("Chat_Total_Accepts")]
    public int ChatTotalAccepts { get; set; } = 0;

    [Column("Chat_Total_Rejects")]
    public int ChatTotalRejects { get; set; } = 0;

    [Column("Chat_Tabs_Shown")]
    public int ChatTabsShown { get; set; } = 0;

    [Column("Tabs_Accepted")]
    public int TabsAccepted { get; set; } = 0;

    [Column("Edit_Requests")]
    public int EditRequests { get; set; } = 0;

    [Column("Ask_Requests")]
    public int AskRequests { get; set; } = 0;

    [Column("Agent_Requests")]
    public int AgentRequests { get; set; } = 0;

    [Column("Cmd_K_Usages")]
    public int CmdKUsages { get; set; } = 0;

    [Column("Subscription_Included_Reqs")]
    public int SubscriptionIncludedReqs { get; set; } = 0;

    [Column("API_Key_Reqs")]
    public int ApiKeyReqs { get; set; } = 0;

    [Column("Usage_Based_Reqs")]
    public int UsageBasedReqs { get; set; } = 0;

    [Column("Bugbot_Usages")]
    public int BugbotUsages { get; set; } = 0;

    [Column("Most_Used_Model")]
    [StringLength(255)]
    public string? MostUsedModel { get; set; }

    [Column("Most_Used_Apply_Extension")]
    [StringLength(255)]
    public string? MostUsedApplyExtension { get; set; }

    [Column("Most_Used_Tab_Extension")]
    [StringLength(255)]
    public string? MostUsedTabExtension { get; set; }

    [Column("Client_Version")]
    [StringLength(255)]
    public string? ClientVersion { get; set; }

    [Column("raw_date")]
    [StringLength(255)]
    public string? RawDate { get; set; }

    [Column("User_Name")]
    [StringLength(255)]
    public string? UserName { get; set; }

    [Column("Work_Email")]
    [StringLength(255)]
    public string? WorkEmail { get; set; }

    [Column("last_updated")]
    [DatabaseGenerated(DatabaseGeneratedOption.Computed)]
    public DateTime LastUpdated { get; set; }
}

[Table("xmlybep_user_identity_mapping")]
public class XmlyBepUserIdentityMapping
{
    [Key]
    [Column("id")]
    public int Id { get; set; }

    [Column("l1")]
    [StringLength(255)]
    public string? L1 { get; set; }

    [Column("l2")]
    [StringLength(255)]
    public string? L2 { get; set; }

    [Column("l3")]
    [StringLength(255)]
    public string? L3 { get; set; }

    [Column("email")]
    [StringLength(255)]
    public string? Email { get; set; }

    [Column("git_user_id")]
    public int? GitUserId { get; set; }

    [Column("user_corp_id")]
    [StringLength(255)]
    public string? UserCorpId { get; set; }
}
