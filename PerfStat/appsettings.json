{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Server=************;Port=33306;Database=ad_other;Uid=root;Pwd=**********;CharSet=utf8mb4;"}, "CronJobs": {"DailyApiCall": {"CronExpression": "0 0 10 * * ?", "TimeZone": "Asia/Shanghai", "ApiUrl": "http://localhost:5141/api/testapi/daily-task", "HttpMethod": "POST", "Headers": {"Content-Type": "application/json", "User-Agent": "PerfStat-CronJob/1.0"}, "RequestBody": "{\"source\":\"cron-job\",\"timestamp\":\"{{timestamp}}\"}"}}}