# 定时任务功能说明

本项目已集成了类似crontab的定时任务功能，基于Quartz.NET实现。

## 功能特性

- ✅ 支持Cron表达式配置定时任务
- ✅ 支持时区设置（默认配置为GMT+8）
- ✅ 支持HTTP API调用
- ✅ 支持自定义请求头和请求体
- ✅ 提供任务状态查询API
- ✅ 支持手动触发任务
- ✅ 完整的日志记录

## 配置说明

### appsettings.json 配置

```json
{
  "CronJobs": {
    "DailyApiCall": {
      "CronExpression": "0 0 10 * * ?",           // 每天上午10点执行
      "TimeZone": "Asia/Shanghai",                 // GMT+8时区
      "ApiUrl": "https://localhost:7000/api/testapi/daily-task",
      "HttpMethod": "POST",
      "Headers": {
        "Content-Type": "application/json",
        "User-Agent": "PerfStat-CronJob/1.0"
      },
      "RequestBody": "{\"source\":\"cron-job\",\"timestamp\":\"{{timestamp}}\"}"
    }
  }
}
```

### Cron表达式格式

格式：`秒 分 时 日 月 星期`

示例：
- `0 0 10 * * ?` - 每天上午10点
- `0 30 9 * * MON-FRI` - 工作日上午9:30
- `0 0 12 1 * ?` - 每月1号中午12点
- `0 0/15 * * * ?` - 每15分钟执行一次

## API接口

### 1. 查看任务状态
```
GET /api/cronjob/status
```

响应示例：
```json
{
  "jobs": [
    {
      "jobName": "DailyApiCallJob",
      "jobGroup": "DEFAULT",
      "triggerName": "DailyApiCallTrigger",
      "triggerState": "Normal",
      "nextFireTime": "2024-01-02 10:00:00 UTC",
      "previousFireTime": "2024-01-01 10:00:00 UTC"
    }
  ],
  "timestamp": "2024-01-01T15:30:00.000Z"
}
```

### 2. 手动触发任务
```
POST /api/cronjob/trigger/{jobName}
```

示例：
```
POST /api/cronjob/trigger/DailyApiCall
```

### 3. 查看任务配置
```
GET /api/cronjob/config
```

### 4. 测试API（用于验证定时任务）
```
POST /api/testapi/daily-task
GET /api/testapi/health
```

## 使用步骤

### 1. 启动应用
```bash
cd PerfStat
dotnet run
```

### 2. 验证定时任务状态
```bash
curl https://localhost:7000/api/cronjob/status
```

### 3. 手动测试任务
```bash
curl -X POST https://localhost:7000/api/cronjob/trigger/DailyApiCall
```

### 4. 查看日志
应用会在控制台输出详细的任务执行日志。

## 添加新的定时任务

### 1. 在appsettings.json中添加配置
```json
{
  "CronJobs": {
    "NewTask": {
      "CronExpression": "0 0 14 * * ?",
      "TimeZone": "Asia/Shanghai",
      "ApiUrl": "https://your-api.com/endpoint",
      "HttpMethod": "GET",
      "Headers": {},
      "RequestBody": ""
    }
  }
}
```

### 2. 创建新的Job类（可选）
如果需要特殊逻辑，可以创建新的Job类：

```csharp
[DisallowConcurrentExecution]
public class NewTaskJob : IJob
{
    // 实现IJob接口
}
```

### 3. 在Program.cs中注册
```csharp
var newJobKey = new JobKey("NewTaskJob");
q.AddJob<NewTaskJob>(opts => opts.WithIdentity(newJobKey));
q.AddTrigger(opts => opts
    .ForJob(newJobKey)
    .WithIdentity("NewTaskTrigger")
    .WithCronSchedule("0 0 14 * * ?", x => x.InTimeZone(TimeZoneInfo.FindSystemTimeZoneById("Asia/Shanghai")))
    .StartNow());
```

## 注意事项

1. **时区设置**：确保服务器时区设置正确，或在配置中明确指定时区
2. **网络访问**：确保应用能够访问目标API
3. **错误处理**：任务执行失败会记录日志，但不会影响后续执行
4. **并发控制**：使用`[DisallowConcurrentExecution]`防止任务重复执行
5. **SSL证书**：如果调用HTTPS API，确保SSL证书有效

## 故障排除

1. **任务未执行**：检查Cron表达式和时区设置
2. **API调用失败**：检查网络连接和目标API状态
3. **配置错误**：检查appsettings.json格式和配置项
4. **日志查看**：查看控制台输出或配置文件日志
