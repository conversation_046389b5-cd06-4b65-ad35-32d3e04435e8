using Microsoft.EntityFrameworkCore;
using PerfStat.Data;
using PerfStat.Jobs;
using PerfStat.Mappings;
using PerfStat.Models;
using PerfStat.Repositories;
using PerfStat.Services;
using Quartz;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
// Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
builder.Services.AddOpenApi();
builder.Services.AddControllers();

// 配置数据库连接
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseMySql(
        builder.Configuration.GetConnectionString("DefaultConnection"),
        ServerVersion.AutoDetect(builder.Configuration.GetConnectionString("DefaultConnection")!)
    ));

// 配置AutoMapper
builder.Services.AddAutoMapper(typeof(AutoMapperProfile));

// 注册Repository
builder.Services.AddScoped(typeof(IRepository<>), typeof(Repository<>));
builder.Services.AddScoped<ICursorUserActivitySummaryRepository, CursorUserActivitySummaryRepository>();
builder.Services.AddScoped<IXmlyBepUserIdentityMappingRepository, XmlyBepUserIdentityMappingRepository>();

// 注册Service
builder.Services.AddScoped<ICursorUserActivitySummaryService, CursorUserActivitySummaryService>();
builder.Services.AddScoped<IXmlyBepUserIdentityMappingService, XmlyBepUserIdentityMappingService>();

// 配置CronJob设置
builder.Services.Configure<CronJobSettings>(
    builder.Configuration);

// 注册HTTP客户端
builder.Services.AddHttpClient<ICronJobService, CronJobService>();

// 注册定时任务服务
builder.Services.AddScoped<ICronJobService, CronJobService>();

// 配置Quartz
builder.Services.AddQuartz(q =>
{
    // 使用内存存储
    q.UseInMemoryStore();

    // 添加DailyApiCallJob
    var dailyJobKey = new JobKey("DailyApiCallJob");
    q.AddJob<DailyApiCallJob>(opts => opts.WithIdentity(dailyJobKey));

    // 配置触发器 - 每天上午10点（GMT+8）
    q.AddTrigger(opts => opts
        .ForJob(dailyJobKey)
        .WithIdentity("DailyApiCallTrigger")
        .WithCronSchedule("0 0 10 * * ?", x => x.InTimeZone(TimeZoneInfo.FindSystemTimeZoneById("Asia/Shanghai")))
        .StartNow());
});

// 添加Quartz托管服务
builder.Services.AddQuartzHostedService(q => q.WaitForJobsToComplete = true);

// 注册IScheduler服务
builder.Services.AddSingleton<IScheduler>(provider =>
{
    var schedulerFactory = provider.GetRequiredService<ISchedulerFactory>();
    return schedulerFactory.GetScheduler().Result;
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
}

app.UseHttpsRedirection();

app.MapControllers();

var summaries = new[]
{
    "Freezing", "Bracing", "Chilly", "Cool", "Mild", "Warm", "Balmy", "Hot", "Sweltering", "Scorching"
};

app.MapGet("/weatherforecast", () =>
{
    var forecast =  Enumerable.Range(1, 5).Select(index =>
        new WeatherForecast
        (
            DateOnly.FromDateTime(DateTime.Now.AddDays(index)),
            Random.Shared.Next(-20, 55),
            summaries[Random.Shared.Next(summaries.Length)]
        ))
        .ToArray();
    return forecast;
})
.WithName("GetWeatherForecast");

app.Run();

record WeatherForecast(DateOnly Date, int TemperatureC, string? Summary)
{
    public int TemperatureF => 32 + (int)(TemperatureC / 0.5556);
}

// 为测试项目提供公共访问
public partial class Program { }
